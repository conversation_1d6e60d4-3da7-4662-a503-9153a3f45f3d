{"status": 1, "data": {"valid": 1, "data": [{"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "1", "packet_type": "resend", "move_id": "1", "time": "1754235941", "data": [{"uid": "688f84257460f", "type": "simpleNote", "log": "Color of ${players} has been chosen according to his/her preferences. ${change_preferences}", "args": {"players": "<b><span style=\"color:#e94190\">pocopocopunpun</span></b>", "change_preferences": {"log": "<a href=\"https://boardgamearena.com/preferences\" target=\"_blank\">${label}</a>", "args": {"i18n": ["label"], "label": "Change my preferences."}}}}, {"uid": "688f84257a960", "type": "gameStateChange", "log": "", "args": {"name": "gameSetup", "description": "Game setup", "type": "manager", "action": "stGameSetup", "transitions": {"": 10}, "active_player": 91975959, "args": null, "reflexion": {"total": {"91975959": null, "94750821": null}}}}, {"uid": "688f84257af53", "type": "gameStateChange", "log": "", "args": {"id": 10, "active_player": 91975959, "args": null, "type": "game", "reflexion": {"total": {"91975959": null, "94750821": null}}, "updateGameProgression": 0}}, {"uid": "688f84257b94e", "type": "factoriesFilled", "log": "A new round begins !", "args": {"factories": [[{"id": 74, "type": 0, "location": "deck", "line": 0, "column": 49}], [{"id": 33, "type": 4, "location": "factory", "line": 0, "column": 1}, {"id": 46, "type": 5, "location": "factory", "line": 0, "column": 1}, {"id": 101, "type": 4, "location": "factory", "line": 0, "column": 1}, {"id": 81, "type": 2, "location": "factory", "line": 0, "column": 1}], [{"id": 59, "type": 3, "location": "factory", "line": 0, "column": 2}, {"id": 68, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 67, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 100, "type": 2, "location": "factory", "line": 0, "column": 2}], [{"id": 66, "type": 3, "location": "factory", "line": 0, "column": 3}, {"id": 11, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 63, "type": 4, "location": "factory", "line": 0, "column": 3}, {"id": 14, "type": 3, "location": "factory", "line": 0, "column": 3}], [{"id": 6, "type": 5, "location": "factory", "line": 0, "column": 4}, {"id": 89, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 80, "type": 1, "location": "factory", "line": 0, "column": 4}, {"id": 82, "type": 5, "location": "factory", "line": 0, "column": 4}], [{"id": 70, "type": 5, "location": "factory", "line": 0, "column": 5}, {"id": 75, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 12, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 69, "type": 2, "location": "factory", "line": 0, "column": 5}]], "remainingTiles": 80}}, {"uid": "688f84257c0b1", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": null, "94750821": null}}}}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "2", "packet_type": "resend", "move_id": "2", "time": "1754235954", "data": [{"uid": "688f84327ef86", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 33, "type": 4, "location": "factory", "line": 0, "column": 1}, {"id": 101, "type": 4, "location": "factory", "line": 0, "column": 1}], "discardedTiles": [{"id": 46, "type": 5, "location": "factory", "line": 0, "column": 1}, {"id": 81, "type": 2, "location": "factory", "line": 0, "column": 1}], "fromFactory": 1}, "h": "9843f3"}, {"uid": "688f84327fce9", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 2, 3, 4, 5], "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": 117, "94750821": "129"}}}, "lock_uuid": "e67c46d1-9550-42e4-832e-0fec1a295edb"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "3", "packet_type": "resend", "move_id": "3", "time": "1754235956", "data": [{"uid": "688f8434161c5", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 33, "type": 4, "location": "hand", "line": 3, "column": 1}, {"id": 101, "type": 4, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "52f57d"}, {"uid": "688f843416a98", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 116, "94750821": "129"}}}, "lock_uuid": "5c239de5-5c57-4991-89d7-549575592f9f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "4", "packet_type": "resend", "move_id": "4", "time": "1754235957", "data": [{"uid": "688f843593857", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 116, "94750821": "129"}}}, "h": "298111"}, {"uid": "688f84359444e", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84359546b", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "116", "94750821": "129"}}}, "lock_uuid": "7dead861-b1e5-4b1d-8eab-4416766f04a4"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "5", "packet_type": "resend", "move_id": null, "time": "1754235976", "data": [{"uid": "688f84487b30e", "type": "wakeupPlayers", "log": "", "args": []}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "6", "packet_type": "resend", "move_id": "5", "time": "1754235999", "data": [{"uid": "688f845fac285", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 14, "type": 3, "location": "factory", "line": 0, "column": 3}, {"id": 66, "type": 3, "location": "factory", "line": 0, "column": 3}], "discardedTiles": [{"id": 11, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 63, "type": 4, "location": "factory", "line": 0, "column": 3}], "fromFactory": 3}, "h": "096ff9"}, {"uid": "688f845fad034", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 3, 4, 5], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": "116", "94750821": 88}}}, "lock_uuid": "1e1b00d5-f744-4d41-88d8-0f135e92160e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "7", "packet_type": "resend", "move_id": "6", "time": "1754236001", "data": [{"uid": "688f846112f0e", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 14, "type": 3, "location": "hand", "line": 3, "column": 1}, {"id": 66, "type": 3, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "0b7fc3"}, {"uid": "688f846113877", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "116", "94750821": 87}}}, "lock_uuid": "c1237e7d-97d5-41db-8c75-eef5c46fc2ff"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "8", "packet_type": "resend", "move_id": "7", "time": "1754236002", "data": [{"uid": "688f8462c7c36", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "116", "94750821": 87}}}, "h": "8feb32"}, {"uid": "688f8462c822f", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8462c8788", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "87"}}}, "lock_uuid": "4878690d-47d0-4c0a-8b42-5109579dab36"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "9", "packet_type": "resend", "move_id": "8", "time": "1754236006", "data": [{"uid": "688f846664aef", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 59, "type": 3, "location": "factory", "line": 0, "column": 2}], "discardedTiles": [{"id": 67, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 68, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 100, "type": 2, "location": "factory", "line": 0, "column": 2}], "fromFactory": 2}, "h": "a06176"}, {"uid": "688f846665865", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 2, 4, 5], "number": 1, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "87"}}}, "lock_uuid": "fe45e79e-490b-4f08-8a8c-2dc2057c8dde"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "10", "packet_type": "resend", "move_id": "9", "time": "1754236007", "data": [{"uid": "688f8467e0646", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 59, "type": 3, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "3603e9"}, {"uid": "688f8467e0dbd", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "87"}}}, "lock_uuid": "c7b6bf78-5cb1-470e-8234-f1ace50d5a90"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "11", "packet_type": "resend", "move_id": "10", "time": "1754236009", "data": [{"uid": "688f8469674be", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 125, "94750821": "87"}}}, "h": "2e359f"}, {"uid": "688f8469678ee", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f846967d12", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": "110"}}}, "lock_uuid": "81319745-c884-45c6-8374-e583bf1792ee"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "12", "packet_type": "resend", "move_id": "11", "time": "1754236010", "data": [{"uid": "688f846aaa9e5", "type": "tilesPlacedOnLine", "log": "", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": null, "i18n": ["color"], "type": 0, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": false}, "h": "664cc1"}, {"uid": "688f846aaa9ed", "type": "firstPlayerToken", "log": "${player_name} took First Player tile and will start next round", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>"}}, {"uid": "688f846aaa9f9", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color} and First Player tile", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 81, "type": 2, "location": "factory", "line": 0, "column": 0}, {"id": 100, "type": 2, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}}, {"uid": "688f846aab719", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 4, 5], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 110}}}, "lock_uuid": "1487bf74-4980-46f0-8581-26f10702886a"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "13", "packet_type": "resend", "move_id": "12", "time": "1754236012", "data": [{"uid": "688f846c4d572", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 4, "lineNumber": 4, "placedTiles": [{"id": 81, "type": 2, "location": "hand", "line": 4, "column": 1}, {"id": 100, "type": 2, "location": "hand", "line": 4, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "78cdc6"}, {"uid": "688f846c4e3dd", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 109}}}, "lock_uuid": "97bc7823-4a1a-48b9-834f-973df06411f2"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "14", "packet_type": "resend", "move_id": "13", "time": "1754236013", "data": [{"uid": "688f846de877d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "125", "94750821": 109}}}, "h": "282244"}, {"uid": "688f846de8c7e", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f846de912f", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "109"}}}, "lock_uuid": "c2fe7060-bda4-40fb-82c3-1b1bcbc11709"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "15", "packet_type": "resend", "move_id": "14", "time": "1754236031", "data": [{"uid": "688f847f4ea15", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 12, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 75, "type": 1, "location": "factory", "line": 0, "column": 5}], "discardedTiles": [{"id": 69, "type": 2, "location": "factory", "line": 0, "column": 5}, {"id": 70, "type": 5, "location": "factory", "line": 0, "column": 5}], "fromFactory": 5}, "h": "da85b1"}, {"uid": "688f847f4f8a0", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 2, 4, 5], "number": 2, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 112, "94750821": "109"}}}, "lock_uuid": "1231c622-9d6c-4e12-8b6b-9c353b72a292"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "16", "packet_type": "resend", "move_id": "15", "time": "1754236032", "data": [{"uid": "688f8480e3765", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 12, "type": 1, "location": "hand", "line": 2, "column": 1}, {"id": 75, "type": 1, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "e44508"}, {"uid": "688f8480e3ff1", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 112, "94750821": "109"}}}, "lock_uuid": "996e8201-ce63-42f9-886d-f5392c90b757"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "17", "packet_type": "resend", "move_id": "16", "time": "1754236035", "data": [{"uid": "688f84833d961", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 110, "94750821": "109"}}}, "h": "2a5bbf"}, {"uid": "688f84833ddf4", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84833e252", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "110", "94750821": "129"}}}, "lock_uuid": "00fd7a5c-850b-46d2-8c61-64b3bfa86bdd"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "18", "packet_type": "resend", "move_id": "17", "time": "1754236044", "data": [{"uid": "688f848c8cc26", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 6, "type": 5, "location": "factory", "line": 0, "column": 4}, {"id": 82, "type": 5, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 80, "type": 1, "location": "factory", "line": 0, "column": 4}, {"id": 89, "type": 4, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "71f8fe"}, {"uid": "688f848c8e974", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 5], "number": 2, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": "110", "94750821": 121}}}, "lock_uuid": "94c8c5a0-6396-4c85-8d73-959448f8842c"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "19", "packet_type": "resend", "move_id": "18", "time": "1754236046", "data": [{"uid": "688f848e3f29a", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 6, "type": 5, "location": "hand", "line": 2, "column": 1}, {"id": 82, "type": 5, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "f552ac"}, {"uid": "688f848e3fddd", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "110", "94750821": 120}}}, "lock_uuid": "ad1b16ee-8dfe-4725-8215-82058fdeee40"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "20", "packet_type": "resend", "move_id": "19", "time": "1754236047", "data": [{"uid": "688f848fe5f63", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "110", "94750821": 120}}}, "h": "2aec11"}, {"uid": "688f848fe6586", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f848fe6b8c", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "120"}}}, "lock_uuid": "50d48a54-cb5c-4cdd-8eb2-0a2a4d771011"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "21", "packet_type": "resend", "move_id": "20", "time": "1754236048", "data": [{"uid": "688f8490dcd10", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 63, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 67, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 89, "type": 4, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "440bee"}, {"uid": "688f8490ddaf8", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3, 4, 5], "number": 3, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "120"}}}, "lock_uuid": "4672d5a4-96c5-41ce-8161-45437005d385"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "22", "packet_type": "resend", "move_id": "21", "time": "1754236050", "data": [{"uid": "688f849258941", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 63, "type": 4, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [{"id": 67, "type": 4, "location": "hand", "line": 0, "column": 1}, {"id": 89, "type": 4, "location": "hand", "line": 0, "column": 2}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "cf639f"}, {"uid": "688f84925943e", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "120"}}}, "lock_uuid": "afce95d0-4773-4500-85bb-fd07166aacf9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "23", "packet_type": "resend", "move_id": "22", "time": "1754236051", "data": [{"uid": "688f8493d3aed", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "120"}}}, "h": "93aa0d"}, {"uid": "688f8493d4062", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f8493d4598", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "393fb481-0aad-46e4-82f3-8340ce8c219f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "24", "packet_type": "resend", "move_id": "23", "time": "1754236052", "data": [{"uid": "688f84949e0a1", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 11, "type": 1, "location": "factory", "line": 0, "column": 0}, {"id": 80, "type": 1, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "c9a19a"}, {"uid": "688f84949ec90", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 5], "number": 2, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 129}}}, "lock_uuid": "b44b7590-dedf-4bb1-89e6-43a69acef0bd"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "25", "packet_type": "resend", "move_id": "24", "time": "1754236054", "data": [{"uid": "688f8496c8cb7", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 11, "type": 1, "location": "hand", "line": 5, "column": 1}, {"id": 80, "type": 1, "location": "hand", "line": 5, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "307763"}, {"uid": "688f8496c94e3", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 128}}}, "lock_uuid": "dac5c3b0-f11a-4167-83ad-51f0cbe06dd2"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "26", "packet_type": "resend", "move_id": "25", "time": "1754236056", "data": [{"uid": "688f849839a14", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 127}}}, "h": "a3dc62"}, {"uid": "688f849839e42", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84983a2c0", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "5b185fc9-bac8-452b-8a9b-4736bd2edafa"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "27", "packet_type": "resend", "move_id": "26", "time": "1754236058", "data": [{"uid": "688f849a0db2c", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 69, "type": 2, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "3b25b4"}, {"uid": "688f849a0f9a5", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 4, 5], "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "9dda2362-ff7a-42f0-8d3c-cc4d79411dea"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "28", "packet_type": "resend", "move_id": "27", "time": "1754236060", "data": [{"uid": "688f849ca1c5f", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on floor line", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 69, "type": 2, "location": "hand", "line": 0, "column": 3}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "b32acb"}, {"uid": "688f849ca328b", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "127"}}}, "lock_uuid": "8c93221c-1c65-454c-892a-60b30caaaf56"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "29", "packet_type": "resend", "move_id": "28", "time": "1754236062", "data": [{"uid": "688f849e9f32d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 126, "94750821": "127"}}}, "h": "ed345b"}, {"uid": "688f849e9f852", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f849e9fc8e", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": "129"}}}, "lock_uuid": "371fb0b4-f0e6-45ad-8a9d-7dc245a06e39"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "30", "packet_type": "resend", "move_id": "29", "time": "1754236063", "data": [{"uid": "688f849f2b4ed", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 46, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 68, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 70, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "f5bdd4"}, {"uid": "688f849f2c407", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1], "number": 3, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": 129}}}, "lock_uuid": "fb1aba5c-227d-47c4-8e1e-894eb8c9a1c9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "31", "packet_type": "resend", "move_id": "30", "time": "1754236064", "data": [{"uid": "688f84a058ead", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 46, "type": 5, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [{"id": 68, "type": 5, "location": "hand", "line": 0, "column": 2}, {"id": 70, "type": 5, "location": "hand", "line": 0, "column": 3}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "ab538e"}, {"uid": "688f84a059653", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": 129}}}, "lock_uuid": "e0f2794a-90d1-4b1a-89cc-a0b394e9085b"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "32", "packet_type": "resend", "move_id": "31", "time": "1754236069", "data": [{"uid": "688f84a53f14b", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "126", "94750821": 125}}}, "h": "f94b4d"}, {"uid": "688f84a53f749", "type": "gameStateChange", "log": "", "args": {"id": 50, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "126", "94750821": "125"}}}}, {"uid": "688f84a53fa99", "type": "gameStateChange", "log": "", "args": {"id": 52, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "126", "94750821": "125"}}}}, {"uid": "688f84a540363", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 59, "type": 3, "location": "line91975959", "line": 1, "column": 1}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 59, "type": 3, "location": "line91975959", "line": 1, "column": 1}], "columnTiles": [{"id": 59, "type": 3, "location": "line91975959", "line": 1, "column": 1}], "points": 1}}, "94750821": {"placedTile": {"id": 46, "type": 5, "location": "line94750821", "line": 1, "column": 3}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 46, "type": 5, "location": "line94750821", "line": 1, "column": 3}], "columnTiles": [{"id": 46, "type": 5, "location": "line94750821", "line": 1, "column": 3}], "points": 1}}}}}, {"uid": "688f84a5403d6", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84a540442", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84a540d09", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 12, "type": 1, "location": "line91975959", "line": 2, "column": 5}, "discardedTiles": [{"id": 75, "type": 1, "location": "line91975959", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 12, "type": 1, "location": "line91975959", "line": 2, "column": 5}], "columnTiles": [{"id": 12, "type": 1, "location": "line91975959", "line": 2, "column": 5}], "points": 1}}, "94750821": {"placedTile": {"id": 6, "type": 5, "location": "line94750821", "line": 2, "column": 4}, "discardedTiles": [{"id": 82, "type": 5, "location": "line94750821", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 6, "type": 5, "location": "line94750821", "line": 2, "column": 4}], "columnTiles": [{"id": 6, "type": 5, "location": "line94750821", "line": 2, "column": 4}], "points": 1}}}}}, {"uid": "688f84a540d82", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84a540dee", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84a5414bb", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 33, "type": 4, "location": "line91975959", "line": 3, "column": 4}, "discardedTiles": [{"id": 101, "type": 4, "location": "line91975959", "line": 3, "column": 2}, {"id": 63, "type": 4, "location": "line91975959", "line": 3, "column": 3}], "pointsDetail": {"rowTiles": [{"id": 33, "type": 4, "location": "line91975959", "line": 3, "column": 4}], "columnTiles": [{"id": 33, "type": 4, "location": "line91975959", "line": 3, "column": 4}], "points": 1}}}}}, {"uid": "688f84a541534", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84a542521", "type": "emptyFloorLine", "log": "", "args": {"floorLines": {"91975959": {"tiles": [{"id": 67, "type": 4, "location": "line91975959", "line": 0, "column": 1}, {"id": 89, "type": 4, "location": "line91975959", "line": 0, "column": 2}, {"id": 69, "type": 2, "location": "line91975959", "line": 0, "column": 3}], "points": -4}, "94750821": {"tiles": [{"id": 74, "type": 0, "location": "line94750821", "line": 0, "column": 1}, {"id": 68, "type": 5, "location": "line94750821", "line": 0, "column": 2}, {"id": 70, "type": 5, "location": "line94750821", "line": 0, "column": 3}], "points": -4}}, "specialFactoryZeroTiles": {"91975959": [], "94750821": []}}}, {"uid": "688f84a5425a0", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "pocopocopunpun", "points": 4}}, {"uid": "688f84a542620", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "<PERSON><PERSON><PERSON>", "points": 4}}, {"uid": "688f84a542ae7", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84a54319a", "type": "gameStateChange", "log": "", "args": {"id": 10, "active_player": 94750821, "args": null, "type": "game", "reflexion": {"total": {"91975959": "126", "94750821": "129"}}, "updateGameProgression": 20}}, {"uid": "688f84a543f57", "type": "factoriesFilled", "log": "A new round begins !", "args": {"factories": [[{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 0}], [{"id": 61, "type": 2, "location": "factory", "line": 0, "column": 1}, {"id": 58, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 21, "type": 1, "location": "factory", "line": 0, "column": 1}, {"id": 71, "type": 2, "location": "factory", "line": 0, "column": 1}], [{"id": 79, "type": 2, "location": "factory", "line": 0, "column": 2}, {"id": 84, "type": 3, "location": "factory", "line": 0, "column": 2}, {"id": 32, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 85, "type": 5, "location": "factory", "line": 0, "column": 2}], [{"id": 9, "type": 5, "location": "factory", "line": 0, "column": 3}, {"id": 30, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 60, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 95, "type": 2, "location": "factory", "line": 0, "column": 3}], [{"id": 40, "type": 2, "location": "factory", "line": 0, "column": 4}, {"id": 51, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 22, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 36, "type": 2, "location": "factory", "line": 0, "column": 4}], [{"id": 83, "type": 5, "location": "factory", "line": 0, "column": 5}, {"id": 53, "type": 4, "location": "factory", "line": 0, "column": 5}, {"id": 45, "type": 3, "location": "factory", "line": 0, "column": 5}, {"id": 43, "type": 4, "location": "factory", "line": 0, "column": 5}]], "remainingTiles": 60}}, {"uid": "688f84a5444c9", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": "129"}}}, "lock_uuid": "11dc8579-f7b2-4dbf-870e-4a02157ae80b"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "33", "packet_type": "resend", "move_id": "32", "time": "1754236086", "data": [{"uid": "688f84b6db9fd", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 30, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 60, "type": 1, "location": "factory", "line": 0, "column": 3}], "discardedTiles": [{"id": 9, "type": 5, "location": "factory", "line": 0, "column": 3}, {"id": 95, "type": 2, "location": "factory", "line": 0, "column": 3}], "fromFactory": 3}, "h": "28014e"}, {"uid": "688f84b6dc7af", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 5], "number": 2, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": 113}}}, "lock_uuid": "db37fd7a-bb48-49ad-8851-75507cf02e1f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "34", "packet_type": "resend", "move_id": "33", "time": "1754236088", "data": [{"uid": "688f84b8577d7", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 30, "type": 1, "location": "hand", "line": 5, "column": 3}, {"id": 60, "type": 1, "location": "hand", "line": 5, "column": 4}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "28f8cc"}, {"uid": "688f84b85816c", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "126", "94750821": 112}}}, "lock_uuid": "ca0bb98a-d6ca-4f09-87be-1d56b89debb3"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "35", "packet_type": "resend", "move_id": "34", "time": "1754236093", "data": [{"uid": "688f84bd3bf24", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "126", "94750821": 108}}}, "h": "b79c68"}, {"uid": "688f84bd3c346", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84bd3c784", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "108"}}}, "lock_uuid": "ce5fa898-1aaa-4bdc-8094-8d6886f8adf9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "36", "packet_type": "resend", "move_id": "35", "time": "1754236094", "data": [{"uid": "688f84bee4de4", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 21, "type": 1, "location": "factory", "line": 0, "column": 1}], "discardedTiles": [{"id": 58, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 61, "type": 2, "location": "factory", "line": 0, "column": 1}, {"id": 71, "type": 2, "location": "factory", "line": 0, "column": 1}], "fromFactory": 1}, "h": "5838aa"}, {"uid": "688f84bee59a7", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 3, 4, 5], "number": 1, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "108"}}}, "lock_uuid": "6dadabac-5067-4c23-82a9-34d71f4c2425"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "37", "packet_type": "resend", "move_id": "36", "time": "1754236096", "data": [{"uid": "688f84c06081c", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 21, "type": 1, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "b31ed8"}, {"uid": "688f84c0612c2", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "108"}}}, "lock_uuid": "3624057f-533e-405d-82be-20bc86122e4a"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "38", "packet_type": "resend", "move_id": "37", "time": "1754236097", "data": [{"uid": "688f84c1c6d51", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "108"}}}, "h": "d34715"}, {"uid": "688f84c1c779e", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84c1c81a2", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "db22b6a5-bfce-48be-82f8-0e9f26155247"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "39", "packet_type": "resend", "move_id": "38", "time": "1754236116", "data": [{"uid": "688f84d49bc28", "type": "tilesPlacedOnLine", "log": "", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": null, "i18n": ["color"], "type": 0, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": false}, "h": "c9b401"}, {"uid": "688f84d49bc33", "type": "firstPlayerToken", "log": "${player_name} took First Player tile and will start next round", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>"}}, {"uid": "688f84d49bc43", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color} and First Player tile", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 61, "type": 2, "location": "factory", "line": 0, "column": 0}, {"id": 71, "type": 2, "location": "factory", "line": 0, "column": 0}, {"id": 95, "type": 2, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}}, {"uid": "688f84d49cd06", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 4], "number": 3, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 111}}}, "lock_uuid": "8f34e7cf-02b4-46af-849b-ddb2f0c3514d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "40", "packet_type": "resend", "move_id": "39", "time": "1754236118", "data": [{"uid": "688f84d693f11", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 4, "lineNumber": 4, "placedTiles": [{"id": 61, "type": 2, "location": "hand", "line": 4, "column": 3}, {"id": 71, "type": 2, "location": "hand", "line": 4, "column": 4}], "discardedTiles": [{"id": 95, "type": 2, "location": "hand", "line": 0, "column": 2}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "1f1fa0"}, {"uid": "688f84d69492c", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 110}}}, "lock_uuid": "8d308fd9-90ae-4d19-8f6c-afc5e13c6b1d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "41", "packet_type": "resend", "move_id": "40", "time": "1754236120", "data": [{"uid": "688f84d80f81d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 109}}}, "h": "0cfbbe"}, {"uid": "688f84d80ff7b", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84d81048e", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "109"}}}, "lock_uuid": "8e459db4-ae58-4f2a-8f26-fc3e7cac45db"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "42", "packet_type": "resend", "move_id": "41", "time": "1754236122", "data": [{"uid": "688f84da1fdb8", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 36, "type": 2, "location": "factory", "line": 0, "column": 4}, {"id": 40, "type": 2, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 22, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 51, "type": 4, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "4f8220"}, {"uid": "688f84da209ff", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 2, 3, 4, 5], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "109"}}}, "lock_uuid": "4533a88b-465c-4ca5-86d4-d7d197e29e46"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "43", "packet_type": "resend", "move_id": "42", "time": "1754236124", "data": [{"uid": "688f84dc256ac", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 36, "type": 2, "location": "hand", "line": 5, "column": 1}, {"id": 40, "type": 2, "location": "hand", "line": 5, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "f7671f"}, {"uid": "688f84dc261af", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "109"}}}, "lock_uuid": "031e789c-f8aa-4da7-8070-a7caa9b997c9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "44", "packet_type": "resend", "move_id": "43", "time": "1754236125", "data": [{"uid": "688f84dd8305f", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 127, "94750821": "109"}}}, "h": "12cdb9"}, {"uid": "688f84dd835b6", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84dd83ad1", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": "129"}}}, "lock_uuid": "1d7ff93f-4bb9-4702-8c61-a4e78f966830"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "45", "packet_type": "resend", "move_id": "44", "time": "1754236126", "data": [{"uid": "688f84de5b053", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 22, "type": 3, "location": "factory", "line": 0, "column": 0}, {"id": 58, "type": 3, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "84dc1e"}, {"uid": "688f84de5be08", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 3], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 129}}}, "lock_uuid": "0c981c60-91ad-41fd-8bc2-0d8dd21cc630"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "46", "packet_type": "resend", "move_id": "45", "time": "1754236128", "data": [{"uid": "688f84e00cbd1", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 22, "type": 3, "location": "hand", "line": 2, "column": 1}, {"id": 58, "type": 3, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "7ea86b"}, {"uid": "688f84e00d60c", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 128}}}, "lock_uuid": "0c4d5b72-1812-46e6-83cb-5f0d57a36353"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "47", "packet_type": "resend", "move_id": "46", "time": "1754236130", "data": [{"uid": "688f84e28ea65", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "127", "94750821": 127}}}, "h": "a24453"}, {"uid": "688f84e28eeea", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84e28f33d", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "9a16ecf4-7284-4ebe-8408-df19759df4c6"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "48", "packet_type": "resend", "move_id": "47", "time": "1754236132", "data": [{"uid": "688f84e49c376", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 43, "type": 4, "location": "factory", "line": 0, "column": 5}, {"id": 53, "type": 4, "location": "factory", "line": 0, "column": 5}], "discardedTiles": [{"id": 45, "type": 3, "location": "factory", "line": 0, "column": 5}, {"id": 83, "type": 5, "location": "factory", "line": 0, "column": 5}], "fromFactory": 5}, "h": "5d6703"}, {"uid": "688f84e49d1da", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 2, 4], "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "3254df9a-feb8-4594-887d-033d2f68b1d0"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "49", "packet_type": "resend", "move_id": "48", "time": "1754236134", "data": [{"uid": "688f84e62b012", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 43, "type": 4, "location": "hand", "line": 2, "column": 1}, {"id": 53, "type": 4, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "aa0c28"}, {"uid": "688f84e62b9e5", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "127"}}}, "lock_uuid": "57e7fabe-b9f0-4497-81f2-024ea63849f8"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "50", "packet_type": "resend", "move_id": "49", "time": "1754236135", "data": [{"uid": "688f84e7bb7c3", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 127, "94750821": "127"}}}, "h": "50756e"}, {"uid": "688f84e7bbfdb", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84e7bc5a2", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": "129"}}}, "lock_uuid": "807eac68-018c-4785-830d-8c4d13466d9d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "51", "packet_type": "resend", "move_id": "50", "time": "1754236136", "data": [{"uid": "688f84e8bee71", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 45, "type": 3, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "6e33cd"}, {"uid": "688f84e8bf917", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 3], "number": 1, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 129}}}, "lock_uuid": "398a6b6d-6006-4c86-8aaa-e7e6b60d02f9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "52", "packet_type": "resend", "move_id": "51", "time": "1754236138", "data": [{"uid": "688f84ea449c9", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 45, "type": 3, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "52d849"}, {"uid": "688f84ea453de", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 128}}}, "lock_uuid": "9741332d-17cc-4a6e-8d49-bc5bdc4074cd"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "53", "packet_type": "resend", "move_id": "52", "time": "1754236140", "data": [{"uid": "688f84ec2c657", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "127", "94750821": 127}}}, "h": "411c6a"}, {"uid": "688f84ec2cb37", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84ec2d081", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "00473c56-ec00-4636-841f-8c70614b733e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "54", "packet_type": "resend", "move_id": "53", "time": "1754236142", "data": [{"uid": "688f84ee231b4", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 79, "type": 2, "location": "factory", "line": 0, "column": 2}], "discardedTiles": [{"id": 32, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 84, "type": 3, "location": "factory", "line": 0, "column": 2}, {"id": 85, "type": 5, "location": "factory", "line": 0, "column": 2}], "fromFactory": 2}, "h": "25b066"}, {"uid": "688f84ee241c1", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3, 4, 5], "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "e342369f-e40f-4522-8f78-66f46502f7a9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "55", "packet_type": "resend", "move_id": "54", "time": "1754236143", "data": [{"uid": "688f84efb6f8a", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 79, "type": 2, "location": "hand", "line": 5, "column": 3}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "d890ca"}, {"uid": "688f84efb769a", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "6155a126-f055-4966-8b6d-0096bf4b2a4a"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "56", "packet_type": "resend", "move_id": "55", "time": "1754236145", "data": [{"uid": "688f84f14a34d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 127, "94750821": "127"}}}, "h": "9d0d63"}, {"uid": "688f84f14a80c", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84f14ae74", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": "129"}}}, "lock_uuid": "ce60570d-54a0-4ae5-8b8a-1cc9112535dc"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "57", "packet_type": "resend", "move_id": "56", "time": "1754236147", "data": [{"uid": "688f84f3e9ab4", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 4, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 9, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 32, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 83, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 85, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "d58c6c"}, {"uid": "688f84f3ea845", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0], "number": 4, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 128}}}, "lock_uuid": "b6c9ef0b-70c7-4362-8d89-5137d57b11f0"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "58", "packet_type": "resend", "move_id": "57", "time": "1754236148", "data": [{"uid": "688f84f4ef0dc", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on floor line", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 4, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 9, "type": 5, "location": "hand", "line": 0, "column": 3}, {"id": 32, "type": 5, "location": "hand", "line": 0, "column": 4}, {"id": 83, "type": 5, "location": "hand", "line": 0, "column": 5}, {"id": 85, "type": 5, "location": "hand", "line": 0, "column": 6}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "150f68"}, {"uid": "688f84f4ef8f3", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 128}}}, "lock_uuid": "e614ae5c-b017-4496-8ae9-90592e4001c9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "59", "packet_type": "resend", "move_id": "58", "time": "1754236150", "data": [{"uid": "688f84f6c471d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "127", "94750821": 127}}}, "h": "f7f642"}, {"uid": "688f84f6c4bb5", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f84f6c511a", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "3c45235b-aa89-4652-874b-45631fb1dd24"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "60", "packet_type": "resend", "move_id": "59", "time": "1754236151", "data": [{"uid": "688f84f7b142d", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 84, "type": 3, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "5290d9"}, {"uid": "688f84f7b1f6f", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3, 4], "number": 1, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "127"}}}, "lock_uuid": "2a539b93-efbf-4965-803b-3d17a5f03ac2"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "61", "packet_type": "resend", "move_id": "60", "time": "1754236153", "data": [{"uid": "688f84f90d530", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 4, "lineNumber": 4, "placedTiles": [{"id": 84, "type": 3, "location": "hand", "line": 4, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "991755"}, {"uid": "688f84f90df15", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "88cf164c-9993-46e4-8075-d32f467d8f15"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "62", "packet_type": "resend", "move_id": "61", "time": "1754236154", "data": [{"uid": "688f84fa8b06c", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "h": "78a74a"}, {"uid": "688f84fa8b63b", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84fa8bb7f", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "e009862e-a55e-4de7-8b7f-590d99d1a908"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "63", "packet_type": "resend", "move_id": "62", "time": "1754236155", "data": [{"uid": "688f84fb33d13", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 51, "type": 4, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "4f3fe2"}, {"uid": "688f84fb347cf", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1], "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 129}}}, "lock_uuid": "5cde4eca-2d31-420a-8a50-5a17d9c05f1d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "64", "packet_type": "resend", "move_id": "63", "time": "1754236158", "data": [{"uid": "688f84fe580eb", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 51, "type": 4, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "6f7eae"}, {"uid": "688f84fe58bde", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 127}}}, "lock_uuid": "ddf2b9c4-6e04-40ab-802e-1ce7da9f3ae9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "65", "packet_type": "resend", "move_id": "64", "time": "1754236159", "data": [{"uid": "688f84ffb7c4e", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 127}}}, "h": "db4cd8"}, {"uid": "688f84ffb83b0", "type": "gameStateChange", "log": "", "args": {"id": 50, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "127"}}}}, {"uid": "688f84ffb870f", "type": "gameStateChange", "log": "", "args": {"id": 52, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "127"}}}}, {"uid": "688f84ffb9233", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 21, "type": 1, "location": "line91975959", "line": 1, "column": 4}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 21, "type": 1, "location": "line91975959", "line": 1, "column": 4}], "columnTiles": [{"id": 21, "type": 1, "location": "line91975959", "line": 1, "column": 4}], "points": 1}}, "94750821": {"placedTile": {"id": 51, "type": 4, "location": "line94750821", "line": 1, "column": 2}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 51, "type": 4, "location": "line94750821", "line": 1, "column": 2}, {"id": 46, "type": 5, "location": "wall94750821", "line": 1, "column": 3}], "columnTiles": [{"id": 51, "type": 4, "location": "line94750821", "line": 1, "column": 2}], "points": 2}}}}}, {"uid": "688f84ffb92cb", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84ffb9350", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 2}}, {"uid": "688f84ffb9e95", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 43, "type": 4, "location": "line91975959", "line": 2, "column": 3}, "discardedTiles": [{"id": 53, "type": 4, "location": "line91975959", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 43, "type": 4, "location": "line91975959", "line": 2, "column": 3}], "columnTiles": [{"id": 43, "type": 4, "location": "line91975959", "line": 2, "column": 3}], "points": 1}}, "94750821": {"placedTile": {"id": 22, "type": 3, "location": "line94750821", "line": 2, "column": 2}, "discardedTiles": [{"id": 58, "type": 3, "location": "line94750821", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 22, "type": 3, "location": "line94750821", "line": 2, "column": 2}], "columnTiles": [{"id": 22, "type": 3, "location": "line94750821", "line": 2, "column": 2}, {"id": 51, "type": 4, "location": "wall94750821", "line": 1, "column": 2}], "points": 2}}}}}, {"uid": "688f84ffb9f34", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84ffb9fae", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 2}}, {"uid": "688f84ffba9d8", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"94750821": {"placedTile": {"id": 14, "type": 3, "location": "line94750821", "line": 3, "column": 3}, "discardedTiles": [{"id": 66, "type": 3, "location": "line94750821", "line": 3, "column": 2}, {"id": 45, "type": 3, "location": "line94750821", "line": 3, "column": 3}], "pointsDetail": {"rowTiles": [{"id": 14, "type": 3, "location": "line94750821", "line": 3, "column": 3}], "columnTiles": [{"id": 14, "type": 3, "location": "line94750821", "line": 3, "column": 3}], "points": 1}}}}}, {"uid": "688f84ffbaa58", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f84ffbb1a9", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"94750821": {"placedTile": {"id": 81, "type": 2, "location": "line94750821", "line": 4, "column": 3}, "discardedTiles": [{"id": 100, "type": 2, "location": "line94750821", "line": 4, "column": 2}, {"id": 61, "type": 2, "location": "line94750821", "line": 4, "column": 3}, {"id": 71, "type": 2, "location": "line94750821", "line": 4, "column": 4}], "pointsDetail": {"rowTiles": [{"id": 81, "type": 2, "location": "line94750821", "line": 4, "column": 3}], "columnTiles": [{"id": 81, "type": 2, "location": "line94750821", "line": 4, "column": 3}, {"id": 14, "type": 3, "location": "wall94750821", "line": 3, "column": 3}], "points": 2}}}}}, {"uid": "688f84ffbb233", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "points": 2}}, {"uid": "688f84ffbbfe3", "type": "emptyFloorLine", "log": "", "args": {"floorLines": {"94750821": {"tiles": [{"id": 74, "type": 0, "location": "line94750821", "line": 0, "column": 1}, {"id": 95, "type": 2, "location": "line94750821", "line": 0, "column": 2}, {"id": 9, "type": 5, "location": "line94750821", "line": 0, "column": 3}, {"id": 32, "type": 5, "location": "line94750821", "line": 0, "column": 4}, {"id": 83, "type": 5, "location": "line94750821", "line": 0, "column": 5}, {"id": 85, "type": 5, "location": "line94750821", "line": 0, "column": 6}], "points": -11}}, "specialFactoryZeroTiles": {"91975959": [], "94750821": []}}}, {"uid": "688f84ffbc062", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "<PERSON><PERSON><PERSON>", "points": 11}}, {"uid": "688f84ffbc6af", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f84ffbce8a", "type": "gameStateChange", "log": "", "args": {"id": 10, "active_player": 94750821, "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}, "updateGameProgression": 40}}, {"uid": "688f84ffbde02", "type": "factoriesFilled", "log": "A new round begins !", "args": {"factories": [[{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 0}], [{"id": 4, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 86, "type": 5, "location": "factory", "line": 0, "column": 1}, {"id": 13, "type": 1, "location": "factory", "line": 0, "column": 1}, {"id": 94, "type": 3, "location": "factory", "line": 0, "column": 1}], [{"id": 44, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 15, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 87, "type": 1, "location": "factory", "line": 0, "column": 2}, {"id": 8, "type": 5, "location": "factory", "line": 0, "column": 2}], [{"id": 37, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 29, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 64, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 50, "type": 2, "location": "factory", "line": 0, "column": 3}], [{"id": 10, "type": 1, "location": "factory", "line": 0, "column": 4}, {"id": 23, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 20, "type": 5, "location": "factory", "line": 0, "column": 4}, {"id": 65, "type": 4, "location": "factory", "line": 0, "column": 4}], [{"id": 56, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 39, "type": 4, "location": "factory", "line": 0, "column": 5}, {"id": 19, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 91, "type": 4, "location": "factory", "line": 0, "column": 5}]], "remainingTiles": 40}}, {"uid": "688f84ffbebb4", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "2a53503b-071c-4b80-8466-da1237b42429"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "66", "packet_type": "resend", "move_id": "65", "time": "1754236170", "data": [{"uid": "688f850a5822a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 37, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 50, "type": 2, "location": "factory", "line": 0, "column": 3}], "discardedTiles": [{"id": 29, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 64, "type": 1, "location": "factory", "line": 0, "column": 3}], "fromFactory": 3}, "h": "797f5b"}, {"uid": "688f850a59067", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 2, 3], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 119}}}, "lock_uuid": "ae9e4677-24c9-4aa7-80e9-81b5e7dc2b27"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "67", "packet_type": "resend", "move_id": "66", "time": "1754236171", "data": [{"uid": "688f850baa877", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 37, "type": 2, "location": "hand", "line": 2, "column": 1}, {"id": 50, "type": 2, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "ebab77"}, {"uid": "688f850babfd3", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 119}}}, "lock_uuid": "7b8e0dab-2136-48f8-83b9-10d2881a4535"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "68", "packet_type": "resend", "move_id": "67", "time": "1754236173", "data": [{"uid": "688f850d8cd8a", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 118}}}, "h": "d72859"}, {"uid": "688f850d8d220", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f850d8d69c", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "118"}}}, "lock_uuid": "465093f2-0a5d-46f6-8a1d-6f0c8b834697"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "69", "packet_type": "resend", "move_id": "68", "time": "1754236174", "data": [{"uid": "688f850e79e0a", "type": "tilesPlacedOnLine", "log": "", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": null, "i18n": ["color"], "type": 0, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": false}, "h": "1a11d4"}, {"uid": "688f850e79e11", "type": "firstPlayerToken", "log": "${player_name} took First Player tile and will start next round", "args": {"playerId": 91975959, "player_name": "pocopocopunpun"}}, {"uid": "688f850e79e1d", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color} and First Player tile", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 29, "type": 1, "location": "factory", "line": 0, "column": 0}, {"id": 64, "type": 1, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}}, {"uid": "688f850e7a8cd", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3], "number": 2, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "118"}}}, "lock_uuid": "7b001bc9-a5a9-44bd-8c85-e37e40262529"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "70", "packet_type": "resend", "move_id": "69", "time": "1754236176", "data": [{"uid": "688f8510cd906", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 29, "type": 1, "location": "hand", "line": 3, "column": 1}, {"id": 64, "type": 1, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "4a7ac8"}, {"uid": "688f8510cea7c", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "118"}}}, "lock_uuid": "d245a520-2093-41be-8e62-a7b0de67699c"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "71", "packet_type": "resend", "move_id": "70", "time": "1754236180", "data": [{"uid": "688f85146afb0", "type": "undoSelectLine", "log": "${player_name} cancels tile placement", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "undo": {"from": null, "tiles": [{"id": 29, "line": 3, "type": 1, "column": 1, "location": "hand"}, {"id": 64, "line": 3, "type": 1, "column": 2, "location": "hand"}], "lastRoundBefore": false, "previousFirstPlayer": null, "takeFromSpecialFactoryZero": null}}, "h": "4ca9b5"}, {"uid": "688f85146cd18", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3], "number": 2, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 125, "94750821": "118"}}}, "lock_uuid": "69b10559-c8e5-4eee-8697-df670c4fa321"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "72", "packet_type": "resend", "move_id": "71", "time": "1754236182", "data": [{"uid": "688f85165fe0b", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 29, "type": 1, "location": "hand", "line": 3, "column": 1}, {"id": 64, "type": 1, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "b6128b"}, {"uid": "688f8516606bb", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 124, "94750821": "118"}}}, "lock_uuid": "50c359e3-6746-4a6c-8410-e223cfa0ef06"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "73", "packet_type": "resend", "move_id": "72", "time": "1754236183", "data": [{"uid": "688f8517bef61", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 124, "94750821": "118"}}}, "h": "5da0b8"}, {"uid": "688f8517bf9ba", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f8517c04ab", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "124", "94750821": "129"}}}, "lock_uuid": "61afb879-3414-4bdc-8cda-effceade06e3"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "74", "packet_type": "resend", "move_id": "73", "time": "1754236184", "data": [{"uid": "688f85188ee23", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 4, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 94, "type": 3, "location": "factory", "line": 0, "column": 1}], "discardedTiles": [{"id": 13, "type": 1, "location": "factory", "line": 0, "column": 1}, {"id": 86, "type": 5, "location": "factory", "line": 0, "column": 1}], "fromFactory": 1}, "h": "c01197"}, {"uid": "688f85188fbec", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 4], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": "124", "94750821": 129}}}, "lock_uuid": "93cbe54a-750a-4d98-8b07-139b31bb0adf"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "75", "packet_type": "resend", "move_id": "74", "time": "1754236185", "data": [{"uid": "688f8519e760a", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 4, "type": 3, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [{"id": 94, "type": 3, "location": "hand", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "0ad9b4"}, {"uid": "688f8519e7dd5", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "124", "94750821": 129}}}, "lock_uuid": "a7661605-43c9-4514-8776-a0a7cf519477"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "76", "packet_type": "resend", "move_id": "75", "time": "1754236187", "data": [{"uid": "688f851b6c0c0", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "124", "94750821": 128}}}, "h": "b21fce"}, {"uid": "688f851b6c64c", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f851b6cd73", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "128"}}}, "lock_uuid": "dd4e34e2-beaa-4e01-86ff-822a10f25fb1"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "77", "packet_type": "resend", "move_id": "76", "time": "1754236188", "data": [{"uid": "688f851ccc65c", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 86, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "25d5a2"}, {"uid": "688f851ccdec5", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 2], "number": 1, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "128"}}}, "lock_uuid": "9526eceb-9eec-46d0-8406-d3154cc916ec"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "78", "packet_type": "resend", "move_id": "77", "time": "1754236190", "data": [{"uid": "688f851e2b387", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 86, "type": 5, "location": "hand", "line": 2, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "a001ce"}, {"uid": "688f851e2bba6", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "128"}}}, "lock_uuid": "93f9b03a-4e67-4d8f-82e5-b93e6c63ece9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "79", "packet_type": "resend", "move_id": "78", "time": "1754236191", "data": [{"uid": "688f851f9b90b", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "128"}}}, "h": "dc702e"}, {"uid": "688f851f9beb0", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f851f9c40e", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "7c4128da-8e56-4407-85dc-0cb31ae17173"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "80", "packet_type": "resend", "move_id": "79", "time": "1754236192", "data": [{"uid": "688f8520c421a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 87, "type": 1, "location": "factory", "line": 0, "column": 2}], "discardedTiles": [{"id": 8, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 15, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 44, "type": 4, "location": "factory", "line": 0, "column": 2}], "fromFactory": 2}, "h": "3b7e46"}, {"uid": "688f8520c4fec", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 3, 4, 5], "number": 1, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 129}}}, "lock_uuid": "9349a6bc-3948-4ba4-8bfc-f903fe44d9d9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "81", "packet_type": "resend", "move_id": "80", "time": "1754236194", "data": [{"uid": "688f85220a961", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 87, "type": 1, "location": "hand", "line": 5, "column": 5}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "fd459f"}, {"uid": "688f85220b421", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 128}}}, "lock_uuid": "95b4f5f8-486d-491f-86ec-6ad6c33a387b"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "82", "packet_type": "resend", "move_id": "81", "time": "1754236195", "data": [{"uid": "688f8523bb010", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 128}}}, "h": "a0435d"}, {"uid": "688f8523bb5a7", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8523<PERSON><PERSON>", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "128"}}}, "lock_uuid": "49e5d12e-c97d-4d3a-831f-c147275b259a"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "83", "packet_type": "resend", "move_id": "82", "time": "1754236197", "data": [{"uid": "688f85251fdd7", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 8, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "8eaf62"}, {"uid": "688f852520f0a", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 2], "number": 1, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "128"}}}, "lock_uuid": "2581dfc0-70a3-4092-8abc-e0a58432a30e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "84", "packet_type": "resend", "move_id": "83", "time": "1754236198", "data": [{"uid": "688f85267be60", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 8, "type": 5, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "3ad534"}, {"uid": "688f85267c643", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "128"}}}, "lock_uuid": "f46a518f-92ea-43bc-85bc-684b15cced9a"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "85", "packet_type": "resend", "move_id": "84", "time": "1754236200", "data": [{"uid": "688f8528554ed", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 127, "94750821": "128"}}}, "h": "174356"}, {"uid": "688f8528559d7", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f852855e9a", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": "129"}}}, "lock_uuid": "28a9255c-0563-4a34-850b-b08b539839be"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "86", "packet_type": "resend", "move_id": "85", "time": "1754236201", "data": [{"uid": "688f8529b98b0", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 23, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 65, "type": 4, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 10, "type": 1, "location": "factory", "line": 0, "column": 4}, {"id": 20, "type": 5, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "045f8d"}, {"uid": "688f8529ba889", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 3, 4], "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 129}}}, "lock_uuid": "b550495e-8a77-422b-8a46-2145a0e70996"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "87", "packet_type": "resend", "move_id": "86", "time": "1754236209", "data": [{"uid": "688f85312f14d", "type": "undoTakeTiles", "log": "${player_name} cancels tile selection", "args": {"playerId": "94750821", "player_name": "<PERSON><PERSON><PERSON>", "undo": {"from": 4, "tiles": [{"id": 23, "line": 0, "type": 4, "column": 4, "location": "factory"}, {"id": 65, "line": 0, "type": 4, "column": 4, "location": "factory"}, {"id": 10, "line": 0, "type": 1, "column": 4, "location": "factory"}, {"id": 20, "line": 0, "type": 5, "column": 4, "location": "factory"}], "lastRoundBefore": null, "previousFirstPlayer": 91975959, "takeFromSpecialFactoryZero": false}, "factoryTilesBefore": [], "repositionTiles": false}, "h": "7dec6c"}, {"uid": "688f853131b80", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 122}}}, "lock_uuid": "8971a86d-d808-404d-81b4-f181af5055cd"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "88", "packet_type": "resend", "move_id": "87", "time": "1754236227", "data": [{"uid": "688f85430785c", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 20, "type": 5, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 10, "type": 1, "location": "factory", "line": 0, "column": 4}, {"id": 23, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 65, "type": 4, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "f32964"}, {"uid": "688f8543088f4", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 3, 4], "number": 1, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 105}}}, "lock_uuid": "63565e5b-f5c0-43e0-8b12-19bb12af9b58"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "89", "packet_type": "resend", "move_id": "88", "time": "1754236228", "data": [{"uid": "688f8544afa2b", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on floor line", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 20, "type": 5, "location": "hand", "line": 0, "column": 2}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "47d848"}, {"uid": "688f8544b0104", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 105}}}, "lock_uuid": "5d0fc2ed-61f2-4f0e-8591-e0b2cf59a796"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "90", "packet_type": "resend", "move_id": "89", "time": "1754236231", "data": [{"uid": "688f8547edc61", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "127", "94750821": 103}}}, "h": "4d3429"}, {"uid": "688f8547ee158", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8547ee8d3", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "103"}}}, "lock_uuid": "f01cfe43-8fe6-44c2-844c-eaf774500fff"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "91", "packet_type": "resend", "move_id": "90", "time": "1754236232", "data": [{"uid": "688f8548d128e", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 4, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 15, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 23, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 44, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 65, "type": 4, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "0ed987"}, {"uid": "688f8548d2b5b", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1], "number": 4, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "103"}}}, "lock_uuid": "14b1b498-c38b-438a-887d-1562daeb13b0"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "92", "packet_type": "resend", "move_id": "91", "time": "1754236235", "data": [{"uid": "688f854b6f20c", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 4, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 15, "type": 4, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [{"id": 23, "type": 4, "location": "hand", "line": 0, "column": 2}, {"id": 44, "type": 4, "location": "hand", "line": 0, "column": 3}, {"id": 65, "type": 4, "location": "hand", "line": 0, "column": 4}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "353d02"}, {"uid": "688f854b6fada", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "103"}}}, "lock_uuid": "e66a30ca-d6aa-4371-8910-0100323701a2"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "93", "packet_type": "resend", "move_id": "92", "time": "1754236236", "data": [{"uid": "688f854cc28a8", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 127, "94750821": "103"}}}, "h": "c46f00"}, {"uid": "688f854cc303d", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f854cc3699", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": "126"}}}, "lock_uuid": "a277e196-1c1a-49b8-8573-4e2b994eb490"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "94", "packet_type": "resend", "move_id": "93", "time": "1754236239", "data": [{"uid": "688f854f565ff", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 39, "type": 4, "location": "factory", "line": 0, "column": 5}, {"id": 91, "type": 4, "location": "factory", "line": 0, "column": 5}], "discardedTiles": [{"id": 19, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 56, "type": 1, "location": "factory", "line": 0, "column": 5}], "fromFactory": 5}, "h": "73680c"}, {"uid": "688f854f5753d", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 3, 4], "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 124}}}, "lock_uuid": "6f34ab20-98d0-4685-8c01-7ab76cbbeeb0"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "95", "packet_type": "resend", "move_id": "94", "time": "1754236242", "data": [{"uid": "688f855243d06", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 39, "type": 4, "location": "hand", "line": 3, "column": 1}, {"id": 91, "type": 4, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "5fb729"}, {"uid": "688f855244612", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "127", "94750821": 122}}}, "lock_uuid": "06dae423-5924-4510-83b5-1d588b346443"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "96", "packet_type": "resend", "move_id": "95", "time": "1754236243", "data": [{"uid": "688f8553e4414", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "127", "94750821": 122}}}, "h": "e5cce1"}, {"uid": "688f8553e4952", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8553e4ecb", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "122"}}}, "lock_uuid": "6b738051-9bff-4670-8a99-1af40fe01c3d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "97", "packet_type": "resend", "move_id": "96", "time": "1754236244", "data": [{"uid": "688f8554c0090", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 4, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 10, "type": 1, "location": "factory", "line": 0, "column": 0}, {"id": 13, "type": 1, "location": "factory", "line": 0, "column": 0}, {"id": 19, "type": 1, "location": "factory", "line": 0, "column": 0}, {"id": 56, "type": 1, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "c99df7"}, {"uid": "688f8554c0be3", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3], "number": 4, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "122"}}}, "lock_uuid": "e3b0a1d0-4245-4e4b-883c-250475d25fda"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "98", "packet_type": "resend", "move_id": "97", "time": "1754236246", "data": [{"uid": "688f855637642", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 4, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 10, "type": 1, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [{"id": 13, "type": 1, "location": "hand", "line": 0, "column": 5}, {"id": 19, "type": 1, "location": "hand", "line": 0, "column": 6}, {"id": 56, "type": 1, "location": "hand", "line": 0, "column": 7}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "214815"}, {"uid": "688f855638079", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "122"}}}, "lock_uuid": "689c6980-3f09-4361-80de-87ac7813dbb6"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "99", "packet_type": "resend", "move_id": "98", "time": "1754236247", "data": [{"uid": "688f8557b8aed", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "122"}}}, "h": "8a24ed"}, {"uid": "688f8557b91ab", "type": "gameStateChange", "log": "", "args": {"id": 50, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "122"}}}}, {"uid": "688f8557b94ee", "type": "gameStateChange", "log": "", "args": {"id": 52, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "122"}}}}, {"uid": "688f8557ba09c", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 15, "type": 4, "location": "line91975959", "line": 1, "column": 2}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 15, "type": 4, "location": "line91975959", "line": 1, "column": 2}, {"id": 59, "type": 3, "location": "wall91975959", "line": 1, "column": 1}], "columnTiles": [{"id": 15, "type": 4, "location": "line91975959", "line": 1, "column": 2}], "points": 2}}, "94750821": {"placedTile": {"id": 4, "type": 3, "location": "line94750821", "line": 1, "column": 1}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 4, "type": 3, "location": "line94750821", "line": 1, "column": 1}, {"id": 51, "type": 4, "location": "wall94750821", "line": 1, "column": 2}, {"id": 46, "type": 5, "location": "wall94750821", "line": 1, "column": 3}], "columnTiles": [{"id": 4, "type": 3, "location": "line94750821", "line": 1, "column": 1}], "points": 3}}}}}, {"uid": "688f8557ba129", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 2}}, {"uid": "688f8557ba1a3", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 3}}, {"uid": "688f8557babd0", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 86, "type": 5, "location": "line91975959", "line": 2, "column": 4}, "discardedTiles": [{"id": 8, "type": 5, "location": "line91975959", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 86, "type": 5, "location": "line91975959", "line": 2, "column": 4}, {"id": 43, "type": 4, "location": "wall91975959", "line": 2, "column": 3}, {"id": 12, "type": 1, "location": "wall91975959", "line": 2, "column": 5}], "columnTiles": [{"id": 86, "type": 5, "location": "line91975959", "line": 2, "column": 4}, {"id": 21, "type": 1, "location": "wall91975959", "line": 1, "column": 4}, {"id": 33, "type": 4, "location": "wall91975959", "line": 3, "column": 4}], "points": 6}}, "94750821": {"placedTile": {"id": 37, "type": 2, "location": "line94750821", "line": 2, "column": 1}, "discardedTiles": [{"id": 50, "type": 2, "location": "line94750821", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 37, "type": 2, "location": "line94750821", "line": 2, "column": 1}, {"id": 22, "type": 3, "location": "wall94750821", "line": 2, "column": 2}], "columnTiles": [{"id": 37, "type": 2, "location": "line94750821", "line": 2, "column": 1}, {"id": 4, "type": 3, "location": "wall94750821", "line": 1, "column": 1}], "points": 4}}}}}, {"uid": "688f8557bac56", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "points": 6}}, {"uid": "688f8557baccd", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "points": 4}}, {"uid": "688f8557bb55d", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 29, "type": 1, "location": "line91975959", "line": 3, "column": 1}, "discardedTiles": [{"id": 64, "type": 1, "location": "line91975959", "line": 3, "column": 2}, {"id": 10, "type": 1, "location": "line91975959", "line": 3, "column": 3}], "pointsDetail": {"rowTiles": [{"id": 29, "type": 1, "location": "line91975959", "line": 3, "column": 1}], "columnTiles": [{"id": 29, "type": 1, "location": "line91975959", "line": 3, "column": 1}], "points": 1}}}}}, {"uid": "688f8557bb5f3", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "points": 1}}, {"uid": "688f8557bc129", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"94750821": {"placedTile": {"id": 11, "type": 1, "location": "line94750821", "line": 5, "column": 3}, "discardedTiles": [{"id": 80, "type": 1, "location": "line94750821", "line": 5, "column": 2}, {"id": 30, "type": 1, "location": "line94750821", "line": 5, "column": 3}, {"id": 60, "type": 1, "location": "line94750821", "line": 5, "column": 4}, {"id": 87, "type": 1, "location": "line94750821", "line": 5, "column": 5}], "pointsDetail": {"rowTiles": [{"id": 11, "type": 1, "location": "line94750821", "line": 5, "column": 3}], "columnTiles": [{"id": 11, "type": 1, "location": "line94750821", "line": 5, "column": 3}, {"id": 81, "type": 2, "location": "wall94750821", "line": 4, "column": 3}, {"id": 14, "type": 3, "location": "wall94750821", "line": 3, "column": 3}], "points": 3}}}}}, {"uid": "688f8557bc1b0", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "points": 3}}, {"uid": "688f8557bcd42", "type": "emptyFloorLine", "log": "", "args": {"floorLines": {"91975959": {"tiles": [{"id": 74, "type": 0, "location": "line91975959", "line": 0, "column": 1}, {"id": 23, "type": 4, "location": "line91975959", "line": 0, "column": 2}, {"id": 44, "type": 4, "location": "line91975959", "line": 0, "column": 3}, {"id": 65, "type": 4, "location": "line91975959", "line": 0, "column": 4}, {"id": 13, "type": 1, "location": "line91975959", "line": 0, "column": 5}, {"id": 19, "type": 1, "location": "line91975959", "line": 0, "column": 6}, {"id": 56, "type": 1, "location": "line91975959", "line": 0, "column": 7}], "points": -14}, "94750821": {"tiles": [{"id": 94, "type": 3, "location": "line94750821", "line": 0, "column": 1}, {"id": 20, "type": 5, "location": "line94750821", "line": 0, "column": 2}], "points": -2}}, "specialFactoryZeroTiles": {"91975959": [], "94750821": []}}}, {"uid": "688f8557bcdc7", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "pocopocopunpun", "points": 14}}, {"uid": "688f8557bce3b", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "<PERSON><PERSON><PERSON>", "points": 2}}, {"uid": "688f8557bd3f1", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8557bdb10", "type": "gameStateChange", "log": "", "args": {"id": 10, "active_player": 91975959, "args": null, "type": "game", "reflexion": {"total": {"91975959": "129", "94750821": "122"}}, "updateGameProgression": 60}}, {"uid": "688f8557be882", "type": "factoriesFilled", "log": "A new round begins !", "args": {"factories": [[{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 0}], [{"id": 90, "type": 4, "location": "factory", "line": 0, "column": 1}, {"id": 24, "type": 2, "location": "factory", "line": 0, "column": 1}, {"id": 57, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 73, "type": 5, "location": "factory", "line": 0, "column": 1}], [{"id": 96, "type": 5, "location": "factory", "line": 0, "column": 2}, {"id": 3, "type": 2, "location": "factory", "line": 0, "column": 2}, {"id": 76, "type": 2, "location": "factory", "line": 0, "column": 2}, {"id": 27, "type": 4, "location": "factory", "line": 0, "column": 2}], [{"id": 1, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 55, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 2, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 7, "type": 3, "location": "factory", "line": 0, "column": 3}], [{"id": 72, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 28, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 52, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 41, "type": 3, "location": "factory", "line": 0, "column": 4}], [{"id": 62, "type": 5, "location": "factory", "line": 0, "column": 5}, {"id": 97, "type": 2, "location": "factory", "line": 0, "column": 5}, {"id": 5, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 17, "type": 5, "location": "factory", "line": 0, "column": 5}]], "remainingTiles": 20}}, {"uid": "688f8557bee87", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "122"}}}, "lock_uuid": "c5d8bc94-5da9-44a5-8980-43bdae98c2b2"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "100", "packet_type": "resend", "move_id": "99", "time": "1754236258", "data": [{"uid": "688f8562ca7b1", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 28, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 41, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 52, "type": 3, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 72, "type": 4, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "ad7f99"}, {"uid": "688f8562cb4a8", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 2, 3, 4], "number": 3, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 119, "94750821": "122"}}}, "lock_uuid": "f9a87f49-8033-44a2-8a51-06f977a6716f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "101", "packet_type": "resend", "move_id": "100", "time": "1754236260", "data": [{"uid": "688f85644bbeb", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 4, "lineNumber": 4, "placedTiles": [{"id": 28, "type": 3, "location": "hand", "line": 4, "column": 2}, {"id": 41, "type": 3, "location": "hand", "line": 4, "column": 3}, {"id": 52, "type": 3, "location": "hand", "line": 4, "column": 4}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "163467"}, {"uid": "688f85644c6d8", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 118, "94750821": "122"}}}, "lock_uuid": "d21a7386-0b01-4484-83d0-b2f832bd6aae"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "102", "packet_type": "resend", "move_id": "101", "time": "1754236262", "data": [{"uid": "688f856600b0c", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 117, "94750821": "122"}}}, "h": "f19065"}, {"uid": "688f856600f65", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f856601416", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "117", "94750821": "129"}}}, "lock_uuid": "94797905-3fec-4097-87c9-b9d4dac86d38"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "103", "packet_type": "resend", "move_id": "102", "time": "1754236263", "data": [{"uid": "688f85678c588", "type": "tilesPlacedOnLine", "log": "", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": null, "i18n": ["color"], "type": 0, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": false}, "h": "1737d7"}, {"uid": "688f85678c594", "type": "firstPlayerToken", "log": "${player_name} took First Player tile and will start next round", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>"}}, {"uid": "688f85678c5a0", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color} and First Player tile", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 72, "type": 4, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}}, {"uid": "688f85678d671", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 2, 3, 4, 5], "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": "117", "94750821": 129}}}, "lock_uuid": "8aec2205-00b9-4301-829c-1e3158383d94"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "104", "packet_type": "resend", "move_id": "103", "time": "1754236265", "data": [{"uid": "688f8569d88e1", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 72, "type": 4, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "c1d682"}, {"uid": "688f8569d9265", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "117", "94750821": 128}}}, "lock_uuid": "52817d3f-9fb8-46b0-82d1-62bf2c574442"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "105", "packet_type": "resend", "move_id": "104", "time": "1754236267", "data": [{"uid": "688f856b4a537", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "117", "94750821": 127}}}, "h": "0a44f8"}, {"uid": "688f856b4aaa1", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f856b4b031", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "c13b93ad-f12b-4a17-88a7-27b17e3514cf"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "106", "packet_type": "resend", "move_id": "105", "time": "1754236271", "data": [{"uid": "688f856f26426", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 1, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 55, "type": 2, "location": "factory", "line": 0, "column": 3}], "discardedTiles": [{"id": 2, "type": 1, "location": "factory", "line": 0, "column": 3}, {"id": 7, "type": 3, "location": "factory", "line": 0, "column": 3}], "fromFactory": 3}, "h": "f375c6"}, {"uid": "688f856f274c0", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 2, 3, 5], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "127"}}}, "lock_uuid": "3932c4e9-d9a5-42c5-8c4c-2bf119c165df"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "107", "packet_type": "resend", "move_id": "106", "time": "1754236272", "data": [{"uid": "688f8570e4b17", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 1, "type": 2, "location": "hand", "line": 5, "column": 4}, {"id": 55, "type": 2, "location": "hand", "line": 5, "column": 5}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "eec125"}, {"uid": "688f8570e52a7", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "127"}}}, "lock_uuid": "b1374526-c874-45dc-8b47-685ee87e4361"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "108", "packet_type": "resend", "move_id": "107", "time": "1754236274", "data": [{"uid": "688f85729acef", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 125, "94750821": "127"}}}, "h": "56c307"}, {"uid": "688f85729b12a", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85729b5c7", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": "129"}}}, "lock_uuid": "31384d0c-ef3d-4c11-8839-d5435c31b338"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "109", "packet_type": "resend", "move_id": "108", "time": "1754236276", "data": [{"uid": "688f857424aa6", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 3, "type": 2, "location": "factory", "line": 0, "column": 2}, {"id": 76, "type": 2, "location": "factory", "line": 0, "column": 2}], "discardedTiles": [{"id": 27, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 96, "type": 5, "location": "factory", "line": 0, "column": 2}], "fromFactory": 2}, "h": "f98a8c"}, {"uid": "688f8574258c4", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 5], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 128}}}, "lock_uuid": "7538a0cd-8690-4109-8ec6-7bba2204c114"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "110", "packet_type": "resend", "move_id": "109", "time": "1754236277", "data": [{"uid": "688f85754fa49", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 3, "type": 2, "location": "hand", "line": 5, "column": 1}, {"id": 76, "type": 2, "location": "hand", "line": 5, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "4e648a"}, {"uid": "688f85755012e", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 128}}}, "lock_uuid": "8977e49c-0c82-43ac-8036-d0cb08f347c8"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "111", "packet_type": "resend", "move_id": "110", "time": "1754236279", "data": [{"uid": "688f8577045d7", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "125", "94750821": 127}}}, "h": "681cc6"}, {"uid": "688f857704aaf", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f85770501d", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "f5f4908f-4c65-4606-8bc1-5d64c1793d0d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "112", "packet_type": "resend", "move_id": "111", "time": "1754236280", "data": [{"uid": "688f8578eaf7d", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 73, "type": 5, "location": "factory", "line": 0, "column": 1}], "discardedTiles": [{"id": 24, "type": 2, "location": "factory", "line": 0, "column": 1}, {"id": 57, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 90, "type": 4, "location": "factory", "line": 0, "column": 1}], "fromFactory": 1}, "h": "08b596"}, {"uid": "688f8578ebede", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 1, 3], "number": 1, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "127"}}}, "lock_uuid": "5b63d407-39a2-4e4f-88d8-a484f8336696"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "113", "packet_type": "resend", "move_id": "112", "time": "1754236282", "data": [{"uid": "688f857a5cdf2", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 73, "type": 5, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "126369"}, {"uid": "688f857a5d704", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "lock_uuid": "de73f073-f9cd-4afd-8640-ed92659d344f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "114", "packet_type": "resend", "move_id": "113", "time": "1754236283", "data": [{"uid": "688f857be784f", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "127"}}}, "h": "24df49"}, {"uid": "688f857be7dcf", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f857be836d", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "b6ba1d99-c2a7-43d1-816d-354a95f42de8"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "115", "packet_type": "resend", "move_id": "114", "time": "1754236284", "data": [{"uid": "688f857cb399a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "selectedTiles": [{"id": 27, "type": 4, "location": "factory", "line": 0, "column": 0}, {"id": 90, "type": 4, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "f5a8b1"}, {"uid": "688f857cb4879", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 2, 4], "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 129}}}, "lock_uuid": "91111986-05f1-40f6-8570-7063b9c78432"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "116", "packet_type": "resend", "move_id": "115", "time": "1754236286", "data": [{"uid": "688f857e73a15", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 27, "type": 4, "location": "hand", "line": 2, "column": 1}, {"id": 90, "type": 4, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "27a1bb"}, {"uid": "688f857e743e9", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 128}}}, "lock_uuid": "1ae06b79-5a1b-4abe-84a3-dec86c577025"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "117", "packet_type": "resend", "move_id": "116", "time": "1754236288", "data": [{"uid": "688f858071aea", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 127}}}, "h": "2846a3"}, {"uid": "688f858072028", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8580725a6", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "127"}}}, "lock_uuid": "1d45ef96-642c-4586-8cf0-744b681e96a1"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "118", "packet_type": "resend", "move_id": "117", "time": "1754236291", "data": [{"uid": "688f8583c3b9a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 7, "type": 3, "location": "factory", "line": 0, "column": 0}, {"id": 57, "type": 3, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "0b7404"}, {"uid": "688f8583c4a0a", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 2, 3], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "127"}}}, "lock_uuid": "e3f2e72c-1796-47bd-8345-5eaa148dec4e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "119", "packet_type": "resend", "move_id": "118", "time": "1754236293", "data": [{"uid": "688f85856d565", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 7, "type": 3, "location": "hand", "line": 2, "column": 1}, {"id": 57, "type": 3, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "2afa70"}, {"uid": "688f85856dd99", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "127"}}}, "lock_uuid": "8d0af602-f02c-46fb-851e-bd59f8c6e774"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "120", "packet_type": "resend", "move_id": "119", "time": "1754236295", "data": [{"uid": "688f85874b1fa", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 125, "94750821": "127"}}}, "h": "de62cc"}, {"uid": "688f85874b78c", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85874bccc", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": "129"}}}, "lock_uuid": "0846b552-c015-44ef-822c-2e889a382bb3"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "121", "packet_type": "resend", "move_id": "120", "time": "1754236300", "data": [{"uid": "688f858c5687a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 5, "type": 1, "location": "factory", "line": 0, "column": 5}], "discardedTiles": [{"id": 17, "type": 5, "location": "factory", "line": 0, "column": 5}, {"id": 62, "type": 5, "location": "factory", "line": 0, "column": 5}, {"id": 97, "type": 2, "location": "factory", "line": 0, "column": 5}], "fromFactory": 5}, "h": "7b4439"}, {"uid": "688f858c57653", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 4], "number": 1, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 125}}}, "lock_uuid": "7ab28b27-ded8-4468-8a3e-987c42c3240c"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "122", "packet_type": "resend", "move_id": "121", "time": "1754236304", "data": [{"uid": "688f8590489ee", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 5, "type": 1, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "d56d88"}, {"uid": "688f8590493bf", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 122}}}, "lock_uuid": "d3198106-be05-4d8f-8847-312af5121966"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "123", "packet_type": "resend", "move_id": "122", "time": "1754236305", "data": [{"uid": "688f8591be41d", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "125", "94750821": 122}}}, "h": "03dec6"}, {"uid": "688f8591be9c0", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f8591bef07", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "122"}}}, "lock_uuid": "e4a922e1-a693-4846-8ef9-41b92e96c22d"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "124", "packet_type": "resend", "move_id": "123", "time": "1754236307", "data": [{"uid": "688f85930cc55", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 17, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 62, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 96, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "5f06f0"}, {"uid": "688f85930dc84", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3], "number": 3, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "122"}}}, "lock_uuid": "307faf03-2f70-4a85-8b2c-33eb762a09b3"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "125", "packet_type": "resend", "move_id": "124", "time": "1754236308", "data": [{"uid": "688f8594444be", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 17, "type": 5, "location": "hand", "line": 3, "column": 1}, {"id": 62, "type": 5, "location": "hand", "line": 3, "column": 2}, {"id": 96, "type": 5, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "a1ef3a"}, {"uid": "688f859444cd7", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "122"}}}, "lock_uuid": "76b4fc39-b1bf-40e9-85b9-c9cf066aff07"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "126", "packet_type": "resend", "move_id": "125", "time": "1754236309", "data": [{"uid": "688f8595aca5c", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "122"}}}, "h": "fb731d"}, {"uid": "688f8595ad045", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f8595adb82", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "fdfdd563-f089-4bab-8492-ccdb2af4f081"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "127", "packet_type": "resend", "move_id": "126", "time": "1754236310", "data": [{"uid": "688f8596b0b21", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 24, "type": 2, "location": "factory", "line": 0, "column": 0}, {"id": 97, "type": 2, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "096e2c"}, {"uid": "688f8596b1d20", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 5], "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 129}}}, "lock_uuid": "83c93b2a-1860-4012-824a-d733db906d83"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "128", "packet_type": "resend", "move_id": "127", "time": "1754236312", "data": [{"uid": "688f8598246bb", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 2, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 24, "type": 2, "location": "hand", "line": 5, "column": 3}, {"id": 97, "type": 2, "location": "hand", "line": 5, "column": 4}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "666da6"}, {"uid": "688f859824e15", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 128}}}, "lock_uuid": "71931b9d-6d57-46f0-8475-5091773f5a9c"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "129", "packet_type": "resend", "move_id": "128", "time": "1754236317", "data": [{"uid": "688f859d08b0e", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 124}}}, "h": "8e0907"}, {"uid": "688f859d0904f", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f859d09588", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "124"}}}, "lock_uuid": "378274bb-ef1a-4940-841d-f5bec81ac446"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "130", "packet_type": "resend", "move_id": "129", "time": "1754236317", "data": [{"uid": "688f859de7ca5", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 2, "type": 1, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "cc47a5"}, {"uid": "688f859de8fd2", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0], "number": 1, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "124"}}}, "lock_uuid": "4fc89cd1-9837-4ebb-8cb2-92eb5ab23b97"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "131", "packet_type": "resend", "move_id": "130", "time": "1754236319", "data": [{"uid": "688f859f49fca", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on floor line", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 2, "type": 1, "location": "hand", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "8573c7"}, {"uid": "688f859f4a8da", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "124"}}}, "lock_uuid": "67eec801-5483-47f1-826d-8de79644609b"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "132", "packet_type": "resend", "move_id": "131", "time": "1754236320", "data": [{"uid": "688f85a09f684", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "124"}}}, "h": "e5ba88"}, {"uid": "688f85a0a016b", "type": "gameStateChange", "log": "", "args": {"id": 50, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "124"}}}}, {"uid": "688f85a0a06f4", "type": "gameStateChange", "log": "", "args": {"id": 52, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "124"}}}}, {"uid": "688f85a0a16dd", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 73, "type": 5, "location": "line91975959", "line": 1, "column": 3}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 73, "type": 5, "location": "line91975959", "line": 1, "column": 3}, {"id": 15, "type": 4, "location": "wall91975959", "line": 1, "column": 2}, {"id": 59, "type": 3, "location": "wall91975959", "line": 1, "column": 1}, {"id": 21, "type": 1, "location": "wall91975959", "line": 1, "column": 4}], "columnTiles": [{"id": 73, "type": 5, "location": "line91975959", "line": 1, "column": 3}, {"id": 43, "type": 4, "location": "wall91975959", "line": 2, "column": 3}], "points": 6}}, "94750821": {"placedTile": {"id": 5, "type": 1, "location": "line94750821", "line": 1, "column": 4}, "discardedTiles": [], "pointsDetail": {"rowTiles": [{"id": 5, "type": 1, "location": "line94750821", "line": 1, "column": 4}, {"id": 46, "type": 5, "location": "wall94750821", "line": 1, "column": 3}, {"id": 51, "type": 4, "location": "wall94750821", "line": 1, "column": 2}, {"id": 4, "type": 3, "location": "wall94750821", "line": 1, "column": 1}], "columnTiles": [{"id": 5, "type": 1, "location": "line94750821", "line": 1, "column": 4}, {"id": 6, "type": 5, "location": "wall94750821", "line": 2, "column": 4}], "points": 6}}}}}, {"uid": "688f85a0a176a", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "points": 6}}, {"uid": "688f85a0a17e7", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "points": 6}}, {"uid": "688f85a0a23ee", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 7, "type": 3, "location": "line91975959", "line": 2, "column": 2}, "discardedTiles": [{"id": 57, "type": 3, "location": "line91975959", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 7, "type": 3, "location": "line91975959", "line": 2, "column": 2}, {"id": 43, "type": 4, "location": "wall91975959", "line": 2, "column": 3}, {"id": 86, "type": 5, "location": "wall91975959", "line": 2, "column": 4}, {"id": 12, "type": 1, "location": "wall91975959", "line": 2, "column": 5}], "columnTiles": [{"id": 7, "type": 3, "location": "line91975959", "line": 2, "column": 2}, {"id": 15, "type": 4, "location": "wall91975959", "line": 1, "column": 2}], "points": 6}}, "94750821": {"placedTile": {"id": 27, "type": 4, "location": "line94750821", "line": 2, "column": 3}, "discardedTiles": [{"id": 90, "type": 4, "location": "line94750821", "line": 2, "column": 2}], "pointsDetail": {"rowTiles": [{"id": 27, "type": 4, "location": "line94750821", "line": 2, "column": 3}, {"id": 22, "type": 3, "location": "wall94750821", "line": 2, "column": 2}, {"id": 37, "type": 2, "location": "wall94750821", "line": 2, "column": 1}, {"id": 6, "type": 5, "location": "wall94750821", "line": 2, "column": 4}], "columnTiles": [{"id": 27, "type": 4, "location": "line94750821", "line": 2, "column": 3}, {"id": 46, "type": 5, "location": "wall94750821", "line": 1, "column": 3}, {"id": 14, "type": 3, "location": "wall94750821", "line": 3, "column": 3}, {"id": 81, "type": 2, "location": "wall94750821", "line": 4, "column": 3}, {"id": 11, "type": 1, "location": "wall94750821", "line": 5, "column": 3}], "points": 9}}}}}, {"uid": "688f85a0a24b7", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 6}}, {"uid": "688f85a0a256e", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 9}}, {"uid": "688f85a0a305a", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 17, "type": 5, "location": "line91975959", "line": 3, "column": 5}, "discardedTiles": [{"id": 62, "type": 5, "location": "line91975959", "line": 3, "column": 2}, {"id": 96, "type": 5, "location": "line91975959", "line": 3, "column": 3}], "pointsDetail": {"rowTiles": [{"id": 17, "type": 5, "location": "line91975959", "line": 3, "column": 5}, {"id": 33, "type": 4, "location": "wall91975959", "line": 3, "column": 4}], "columnTiles": [{"id": 17, "type": 5, "location": "line91975959", "line": 3, "column": 5}, {"id": 12, "type": 1, "location": "wall91975959", "line": 2, "column": 5}], "points": 4}}, "94750821": {"placedTile": {"id": 39, "type": 4, "location": "line94750821", "line": 3, "column": 4}, "discardedTiles": [{"id": 91, "type": 4, "location": "line94750821", "line": 3, "column": 2}, {"id": 72, "type": 4, "location": "line94750821", "line": 3, "column": 3}], "pointsDetail": {"rowTiles": [{"id": 39, "type": 4, "location": "line94750821", "line": 3, "column": 4}, {"id": 14, "type": 3, "location": "wall94750821", "line": 3, "column": 3}], "columnTiles": [{"id": 39, "type": 4, "location": "line94750821", "line": 3, "column": 4}, {"id": 6, "type": 5, "location": "wall94750821", "line": 2, "column": 4}, {"id": 5, "type": 1, "location": "wall94750821", "line": 1, "column": 4}], "points": 5}}}}}, {"uid": "688f85a0a30de", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "points": 4}}, {"uid": "688f85a0a3156", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "Yellow", "i18n": ["color"], "type": 4, "preserve": {"2": "type"}, "points": 5}}, {"uid": "688f85a0a3747", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 84, "type": 3, "location": "line91975959", "line": 4, "column": 4}, "discardedTiles": [{"id": 28, "type": 3, "location": "line91975959", "line": 4, "column": 2}, {"id": 41, "type": 3, "location": "line91975959", "line": 4, "column": 3}, {"id": 52, "type": 3, "location": "line91975959", "line": 4, "column": 4}], "pointsDetail": {"rowTiles": [{"id": 84, "type": 3, "location": "line91975959", "line": 4, "column": 4}], "columnTiles": [{"id": 84, "type": 3, "location": "line91975959", "line": 4, "column": 4}, {"id": 33, "type": 4, "location": "wall91975959", "line": 3, "column": 4}, {"id": 86, "type": 5, "location": "wall91975959", "line": 2, "column": 4}, {"id": 21, "type": 1, "location": "wall91975959", "line": 1, "column": 4}], "points": 4}}}}}, {"uid": "688f85a0a37ca", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "points": 4}}, {"uid": "688f85a0a40c7", "type": "placeTileOnWall", "log": "", "args": {"completeLines": {"91975959": {"placedTile": {"id": 36, "type": 2, "location": "line91975959", "line": 5, "column": 4}, "discardedTiles": [{"id": 40, "type": 2, "location": "line91975959", "line": 5, "column": 2}, {"id": 79, "type": 2, "location": "line91975959", "line": 5, "column": 3}, {"id": 1, "type": 2, "location": "line91975959", "line": 5, "column": 4}, {"id": 55, "type": 2, "location": "line91975959", "line": 5, "column": 5}], "pointsDetail": {"rowTiles": [{"id": 36, "type": 2, "location": "line91975959", "line": 5, "column": 4}], "columnTiles": [{"id": 36, "type": 2, "location": "line91975959", "line": 5, "column": 4}, {"id": 84, "type": 3, "location": "wall91975959", "line": 4, "column": 4}, {"id": 33, "type": 4, "location": "wall91975959", "line": 3, "column": 4}, {"id": 86, "type": 5, "location": "wall91975959", "line": 2, "column": 4}, {"id": 21, "type": 1, "location": "wall91975959", "line": 1, "column": 4}], "points": 5}}}}}, {"uid": "688f85a0a418b", "type": "placeTileOnWallTextLogDetails", "log": "${player_name} places ${number} ${color} and gains ${points} point(s)", "args": {"player_name": "pocopocopunpun", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "points": 5}}, {"uid": "688f85a0a4d66", "type": "emptyFloorLine", "log": "", "args": {"floorLines": {"91975959": {"tiles": [{"id": 2, "type": 1, "location": "line91975959", "line": 0, "column": 1}], "points": -1}, "94750821": {"tiles": [{"id": 74, "type": 0, "location": "line94750821", "line": 0, "column": 1}], "points": -1}}, "specialFactoryZeroTiles": {"91975959": [], "94750821": []}}}, {"uid": "688f85a0a4dfc", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "pocopocopunpun", "points": 1}}, {"uid": "688f85a0a4e87", "type": "emptyFloorLineTextLogDetails", "log": "${player_name} loses ${points} point(s) with Floor line", "args": {"player_name": "<PERSON><PERSON><PERSON>", "points": 1}}, {"uid": "688f85a0a5491", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85a0a5bc8", "type": "gameStateChange", "log": "", "args": {"id": 10, "active_player": 94750821, "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}, "updateGameProgression": 80}}, {"uid": "688f85a0a7c9d", "type": "factoriesFilled", "log": "A new round begins !", "args": {"factories": [[{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 0}], [{"id": 98, "type": 3, "location": "factory", "line": 0, "column": 1}, {"id": 25, "type": 2, "location": "factory", "line": 0, "column": 1}, {"id": 31, "type": 5, "location": "factory", "line": 0, "column": 1}, {"id": 38, "type": 3, "location": "factory", "line": 0, "column": 1}], [{"id": 77, "type": 3, "location": "factory", "line": 0, "column": 2}, {"id": 34, "type": 1, "location": "factory", "line": 0, "column": 2}, {"id": 49, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 48, "type": 2, "location": "factory", "line": 0, "column": 2}], [{"id": 99, "type": 4, "location": "factory", "line": 0, "column": 3}, {"id": 78, "type": 2, "location": "factory", "line": 0, "column": 3}, {"id": 54, "type": 5, "location": "factory", "line": 0, "column": 3}, {"id": 42, "type": 3, "location": "factory", "line": 0, "column": 3}], [{"id": 35, "type": 5, "location": "factory", "line": 0, "column": 4}, {"id": 16, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 26, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 93, "type": 3, "location": "factory", "line": 0, "column": 4}], [{"id": 92, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 47, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 18, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 88, "type": 5, "location": "factory", "line": 0, "column": 5}]], "remainingTiles": 0}}, {"uid": "688f85a0a8f5a", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "129"}}}, "lock_uuid": "7d529eef-87c0-4415-86d5-5de11694b6e9"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "133", "packet_type": "resend", "move_id": "132", "time": "1754236350", "data": [{"uid": "688f85be5e4e8", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "selectedTiles": [{"id": 18, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 47, "type": 1, "location": "factory", "line": 0, "column": 5}, {"id": 92, "type": 1, "location": "factory", "line": 0, "column": 5}], "discardedTiles": [{"id": 88, "type": 5, "location": "factory", "line": 0, "column": 5}], "fromFactory": 5}, "h": "a014a3"}, {"uid": "688f85be5f43f", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 2, 3, 4], "number": 3, "color": "Black", "i18n": ["color"], "type": 1}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 100}}}, "lock_uuid": "cd41b877-bb16-4576-84b2-18a52800b96f"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "134", "packet_type": "resend", "move_id": "133", "time": "1754236352", "data": [{"uid": "688f85c0bfc2d", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 3, "color": "Black", "i18n": ["color"], "type": 1, "preserve": {"2": "type"}, "line": 2, "lineNumber": 2, "placedTiles": [{"id": 18, "type": 1, "location": "hand", "line": 2, "column": 1}, {"id": 47, "type": 1, "location": "hand", "line": 2, "column": 2}], "discardedTiles": [{"id": 92, "type": 1, "location": "hand", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "1204c2"}, {"uid": "688f85c0bfede", "type": "lastRound", "log": "${player_name} will complete a line, it's last turn !", "args": {"playerId": "94750821", "player_name": "<PERSON><PERSON><PERSON>"}}, {"uid": "688f85c0c0a14", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 99}}}, "lock_uuid": "da565bae-dd03-400b-87b8-a3c1d49497dc"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "135", "packet_type": "resend", "move_id": "134", "time": "1754236354", "data": [{"uid": "688f85c2534d5", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 98}}}, "h": "a8406f"}, {"uid": "688f85c25394c", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f85c253de5", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "98"}}}, "lock_uuid": "8abe9eb9-cd14-4e8a-85a7-37241414476c"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "136", "packet_type": "resend", "move_id": "135", "time": "1754236355", "data": [{"uid": "688f85c37bf3d", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 26, "type": 3, "location": "factory", "line": 0, "column": 4}, {"id": 93, "type": 3, "location": "factory", "line": 0, "column": 4}], "discardedTiles": [{"id": 16, "type": 4, "location": "factory", "line": 0, "column": 4}, {"id": 35, "type": 5, "location": "factory", "line": 0, "column": 4}], "fromFactory": 4}, "h": "936773"}, {"uid": "688f85c37cb5b", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3, 5], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "98"}}}, "lock_uuid": "9a83391f-926a-4316-8813-44c6833a418e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "137", "packet_type": "resend", "move_id": "136", "time": "1754236357", "data": [{"uid": "688f85c501cd6", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 26, "type": 3, "location": "hand", "line": 3, "column": 1}, {"id": 93, "type": 3, "location": "hand", "line": 3, "column": 2}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "3e51d7"}, {"uid": "688f85c502d72", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "98"}}}, "lock_uuid": "4b952898-5a3f-4e2e-8b38-822663e42302"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "138", "packet_type": "resend", "move_id": "137", "time": "1754236358", "data": [{"uid": "688f85c684055", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 128, "94750821": "98"}}}, "h": "2e9ab4"}, {"uid": "688f85c6849b0", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85c68530e", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": "121"}}}, "lock_uuid": "4d038092-6a7b-462f-84ee-8c6fe217f5b5"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "139", "packet_type": "resend", "move_id": "138", "time": "1754236361", "data": [{"uid": "688f85c98f473", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 78, "type": 2, "location": "factory", "line": 0, "column": 3}], "discardedTiles": [{"id": 42, "type": 3, "location": "factory", "line": 0, "column": 3}, {"id": 54, "type": 5, "location": "factory", "line": 0, "column": 3}, {"id": 99, "type": 4, "location": "factory", "line": 0, "column": 3}], "fromFactory": 3}, "h": "8680c4"}, {"uid": "688f85c9902cc", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 1, 3, 5], "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 119}}}, "lock_uuid": "d84c1748-2229-457c-8f91-f8ab02b3491e"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "140", "packet_type": "resend", "move_id": "139", "time": "1754236362", "data": [{"uid": "688f85cad9fbe", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 1, "lineNumber": 1, "placedTiles": [{"id": 78, "type": 2, "location": "hand", "line": 1, "column": 1}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "2f16a0"}, {"uid": "688f85cada816", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "128", "94750821": 119}}}, "lock_uuid": "e02f020f-8296-40c5-8410-2d2d4f604873"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "141", "packet_type": "resend", "move_id": "140", "time": "1754236364", "data": [{"uid": "688f85ccc0799", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "128", "94750821": 118}}}, "h": "bc9ed3"}, {"uid": "688f85ccc0bd5", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f85ccc1052", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "118"}}}, "lock_uuid": "a20ce87f-e270-4725-830d-3566ebb66df5"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "142", "packet_type": "resend", "move_id": "141", "time": "1754236365", "data": [{"uid": "688f85cd93db3", "type": "tilesPlacedOnLine", "log": "", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 1, "color": null, "i18n": ["color"], "type": 0, "preserve": {"2": "type"}, "line": 0, "lineNumber": 0, "placedTiles": [], "discardedTiles": [{"id": 74, "type": 0, "location": "factory", "line": 0, "column": 1}], "discardedTilesToSpecialFactoryZero": [], "fromHand": false}, "h": "870150"}, {"uid": "688f85cd93dbe", "type": "firstPlayerToken", "log": "${player_name} took First Player tile and will start next round", "args": {"playerId": 91975959, "player_name": "pocopocopunpun"}}, {"uid": "688f85cd93dd0", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color} and First Player tile", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "selectedTiles": [{"id": 35, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 54, "type": 5, "location": "factory", "line": 0, "column": 0}, {"id": 88, "type": 5, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}}, {"uid": "688f85cd94a5b", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 4, 5], "number": 3, "color": "Red", "i18n": ["color"], "type": 5}, "type": "activeplayer", "reflexion": {"total": {"91975959": 129, "94750821": "118"}}}, "lock_uuid": "09915010-de10-4fa2-82f2-321cc01675fe"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "143", "packet_type": "resend", "move_id": "142", "time": "1754236367", "data": [{"uid": "688f85cfb11a7", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 3, "color": "Red", "i18n": ["color"], "type": 5, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 35, "type": 5, "location": "hand", "line": 5, "column": 1}, {"id": 54, "type": 5, "location": "hand", "line": 5, "column": 2}, {"id": 88, "type": 5, "location": "hand", "line": 5, "column": 3}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "fc7803"}, {"uid": "688f85cfb1a0d", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 128, "94750821": "118"}}}, "lock_uuid": "bb3083d2-0a19-4f1c-8538-15b7a77bcf41"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "144", "packet_type": "resend", "move_id": "143", "time": "1754236371", "data": [{"uid": "688f85d302ece", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 125, "94750821": "118"}}}, "h": "9fe480"}, {"uid": "688f85d303443", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85d30399b", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": "129"}}}, "lock_uuid": "682ed855-8bfc-4139-8769-dc3d0b3a0c50"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "145", "packet_type": "resend", "move_id": "144", "time": "1754236372", "data": [{"uid": "688f85d405d3a", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "selectedTiles": [{"id": 48, "type": 2, "location": "factory", "line": 0, "column": 2}], "discardedTiles": [{"id": 34, "type": 1, "location": "factory", "line": 0, "column": 2}, {"id": 49, "type": 4, "location": "factory", "line": 0, "column": 2}, {"id": 77, "type": 3, "location": "factory", "line": 0, "column": 2}], "fromFactory": 2}, "h": "14c4b7"}, {"uid": "688f85d406d8d", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "94750821", "args": {"lines": [0, 3, 5], "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2}, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 129}}}, "lock_uuid": "3230076a-c1c0-4045-8e0f-e71b25225d35"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "146", "packet_type": "resend", "move_id": "145", "time": "1754236373", "data": [{"uid": "688f85d5623f3", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 94750821, "player_name": "<PERSON><PERSON><PERSON>", "number": 1, "color": "<PERSON><PERSON>", "i18n": ["color"], "type": 2, "preserve": {"2": "type"}, "line": 5, "lineNumber": 5, "placedTiles": [{"id": 48, "type": 2, "location": "hand", "line": 5, "column": 5}], "discardedTiles": [], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "f9b1a7"}, {"uid": "688f85d562cd2", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "94750821", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": 129}}}, "lock_uuid": "8f56e304-bb1d-4c83-8982-4d9b3f2f7efb"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "147", "packet_type": "resend", "move_id": "146", "time": "1754236374", "data": [{"uid": "688f85d6db2a1", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "94750821", "args": null, "type": "game", "reflexion": {"total": {"91975959": "125", "94750821": 129}}}, "h": "0abde5"}, {"uid": "688f85d6db837", "type": "updateReflexionTime", "log": "", "args": {"player_id": 91975959, "delta": "23", "max": "129"}}, {"uid": "688f85d6dbca6", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 91975959, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "129", "94750821": "129"}}}, "lock_uuid": "71c67a17-bbe3-411e-8ac2-7be116c97e37"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "148", "packet_type": "resend", "move_id": "147", "time": "1754236377", "data": [{"uid": "688f85d99155d", "type": "tilesSelected", "log": "${player_name} takes ${number} ${color}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "selectedTiles": [{"id": 42, "type": 3, "location": "factory", "line": 0, "column": 0}, {"id": 77, "type": 3, "location": "factory", "line": 0, "column": 0}], "discardedTiles": [], "fromFactory": 0}, "h": "154ff1"}, {"uid": "688f85d99277e", "type": "gameStateChange", "log": "", "args": {"id": 30, "active_player": "91975959", "args": {"lines": [0, 3], "number": 2, "color": "Blue", "i18n": ["color"], "type": 3}, "type": "activeplayer", "reflexion": {"total": {"91975959": 127, "94750821": "129"}}}, "lock_uuid": "2a8f76ba-cb36-4bae-8bf5-b1c727ef77ff"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "149", "packet_type": "resend", "move_id": "148", "time": "1754236379", "data": [{"uid": "688f85db2450a", "type": "tilesPlacedOnLine", "log": "${player_name} places ${number} ${color} on line ${lineNumber}", "args": {"playerId": 91975959, "player_name": "pocopocopunpun", "number": 2, "color": "Blue", "i18n": ["color"], "type": 3, "preserve": {"2": "type"}, "line": 3, "lineNumber": 3, "placedTiles": [{"id": 42, "type": 3, "location": "hand", "line": 3, "column": 3}], "discardedTiles": [{"id": 77, "type": 3, "location": "hand", "line": 0, "column": 2}], "discardedTilesToSpecialFactoryZero": [], "fromHand": true}, "h": "ac3ef5"}, {"uid": "688f85db24e29", "type": "gameStateChange", "log": "", "args": {"id": 31, "active_player": "91975959", "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": 126, "94750821": "129"}}}, "lock_uuid": "6dde0756-161f-4267-8bc7-d83b376993fc"}]}, {"channel": "/table/t709985753", "table_id": "709985753", "packet_id": "150", "packet_type": "resend", "move_id": "149", "time": "1754236381", "data": [{"uid": "688f85dd1024a", "type": "gameStateChange", "log": "", "args": {"id": 80, "active_player": "91975959", "args": null, "type": "game", "reflexion": {"total": {"91975959": 125, "94750821": "129"}}}, "h": "3229a5"}, {"uid": "688f85dd106d9", "type": "updateReflexionTime", "log": "", "args": {"player_id": 94750821, "delta": "23", "max": "129"}}, {"uid": "688f85dd10c06", "type": "gameStateChange", "log": "", "args": {"id": 20, "active_player": 94750821, "args": null, "type": "activeplayer", "reflexion": {"total": {"91975959": "125", "94750821": "129"}}}, "lock_uuid": "e72d1c9d-d5b8-4a7c-8775-919dbf695d0d"}]}]}}