const {I18nTranslations: I18nTranslationsClass} = require('./azul');

describe('I18nTranslations matchAndParse', () => {
    beforeAll(() => {
        // Mock translations for tests
        const mockTranslations = {
            // English patterns
            '${player_name} places ${number} ${color} on line ${lineNumber}': '${player_name} places ${number} ${color} on line ${lineNumber}',
            '${player_name} cancels tile placement': '${player_name} cancels tile placement',
            '${player_name} places ${number} ${color} on floor line': '${player_name} places ${number} ${color} on floor line',
            'A new round begins !': 'A new round begins !',
            'Replay last moves': 'Replay last moves',

            // Polish patterns
            '${player_name} umieszcza ${number} ${color} na linii ${lineNumber}': '${player_name} umieszcza ${number} ${color} na linii ${lineNumber}',
            '${player_name} anuluje umieszczenie': '${player_name} anuluje umieszczenie',
            '${player_name} umieszcza ${number} ${color} na linii podłogi': '${player_name} umieszcza ${number} ${color} na linii podłogi',
            'Rozpoczyna się nowa runda!': 'Rozpoczyna się nowa runda!',
        };

        // Mock operationTypeByKey for Polish patterns
        const mockOperationTypeByKey = {
            // English patterns
            '${player_name} places ${number} ${color} on line ${lineNumber}': 'tile_placed_on_line',
            '${player_name} cancels tile placement': 'cancels_tile_placement',
            '${player_name} places ${number} ${color} on floor line': 'tile_placed_on_floor_line',
            'A new round begins !': 'new_round_starts',
            'Replay last moves': 'replay_last_moves',

            // Polish patterns
            '${player_name} umieszcza ${number} ${color} na linii ${lineNumber}': 'tile_placed_on_line',
            '${player_name} anuluje umieszczenie': 'cancels_tile_placement',
            '${player_name} umieszcza ${number} ${color} na linii podłogi': 'tile_placed_on_floor_line',
            'Rozpoczyna się nowa runda!': 'new_round_starts',
        };

        // Add mock translations to the class
        Object.assign(I18nTranslationsClass.translations, mockTranslations);
        Object.assign(I18nTranslationsClass.operationTypeByKey, mockOperationTypeByKey);
    });
    // Create a fake DOM + navigator for getLanguage()
    function setLanguage(langCode: string) {
        // Directly modify the document and navigator objects that Jest/jsdom provides
        Object.defineProperty(document.documentElement, 'getAttribute', {
            value: (name: string) => {
                if (name === 'lang' || name === 'xml:lang' || name === 'xmlns:lang') {
                    return langCode;
                }
                return null;
            },
            writable: true
        });

        Object.defineProperty(navigator, 'language', {
            value: langCode,
            writable: true
        });
    }

    beforeEach(() => {
        // reset mocks between tests
        jest.resetAllMocks && jest.resetAllMocks();
    });

    test('detects cancel in English', () => {
        setLanguage('en');
        const html = `<div class="roundedbox"><span class="playername" style="color:#008000;">GeoDer</span> cancels tile placement</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('cancels_tile_placement');
        expect(parsed!.player_name).toBe('GeoDer');
    });

    test('detects cancel in Polish', () => {
        setLanguage('pl');

        const html = `<div class="roundedbox"><span class="playername" style="color:#008000;">GeoDer</span> anuluje umieszczenie</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('cancels_tile_placement');
        expect(parsed!.player_name).toBe('GeoDer');
    });

    test('detects placement on line (English) with tiles + line number', () => {
        setLanguage('en');

        const html =
            `<div class="roundedbox"><!--PNS--><span class="playername" style="color:#008000;">GeoDer</span><!--PNE--> places <div class="tile tile1"></div><div class="tile tile1"></div> on line <strong>2</strong></div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('tile_placed_on_line');
        expect(parsed!.player_name).toBe('GeoDer');
        expect(parsed!.number).toBe(2);
        expect(parsed!.color).toBe(1);
        expect(parsed!.lineNumber).toBe(2);
    });

    test('detects placement on line (Polish) with tiles + line number', () => {
        setLanguage('pl');

        const html =
            `<div class="roundedbox">` +
            `<span class="playername">GeoDer</span> umieszcza ` +
            `<div class="tile tile1"></div><div class="tile tile1"></div><div class="tile tile1"></div> ` + // 3 tiles
            `na linii <strong>5</strong>` +
            `</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('tile_placed_on_line');
        expect(parsed!.player_name).toBe('GeoDer');
        expect(parsed!.number).toBe(3);
        expect(parsed!.color).toBe(1);
        expect(parsed!.lineNumber).toBe(5);
    });

    test('detects floor line placement (English)', () => {
        setLanguage('en');
        const html =
            `<div class="roundedbox">` +
            `<span class="playername">Ariadne</span> places ` +
            `<div class="tile tile2"></div> ` +
            `on floor line` +
            `</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        // operation type mapped in class
        expect(parsed!.type).toBe('tile_placed_on_floor_line');
        expect(parsed!.player_name).toBe('Ariadne');
        expect(parsed!.color).toBe(2);
        expect(parsed!.number).toBe(1);
    });

    test('detects floor line placement (Polish)', () => {
        setLanguage('pl');
        const html =
            `<div class="roundedbox"><!--PNS--><span class="playername" style="color:#008000;">Ariadne</span><!--PNE--> umieszcza <div class="tile tile4"></div><div class="tile tile4"></div> na linii podłogi</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('tile_placed_on_floor_line');
        expect(parsed!.player_name).toBe('Ariadne');
        expect(parsed!.color).toBe(4);
        expect(parsed!.number).toBe(2);
    });

    test('detects new round (English)', () => {
        setLanguage('en');
        const html =
            `<div class="roundedbox">A new round begins !</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('new_round_starts');
    });

    test('detects new round (Polish)', () => {
        setLanguage('pl');
        const html =
            `<div class="roundedbox">Rozpoczyna się nowa runda!</div>`;

        const parsed = I18nTranslationsClass.matchAndParse(html);

        expect(parsed).not.toBeNull();
        expect(parsed!.type).toBe('new_round_starts');
    });
});
