.azul-tile-counter {
    position: absolute;
    top: 0;
    display: flex;
    flex-direction: row;
    gap: 4px; /* Space between tiles */
    align-items: center;
    margin: 8px 0;
}

/* Style for the tile elements */
.azul-tile-counter .tile {
    position: relative;
    width: 32px;
    height: 32px;
    border: 2px solid #888;
    border-radius: 6px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    color: black;
}

.azul-tile-counter .tile.tile1 {
    color: white;
}
